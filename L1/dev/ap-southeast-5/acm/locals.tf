locals {
  project    = "tabstudio"
  env        = "dev"
  region     = "ap-southeast-5"
  team       = "infra"
  managed_by = "terraform"
  account_id = "************"
  default_tags = {
    project   = local.project
    env       = local.env
    team      = local.team
    managedBy = local.managed_by
    region    = local.region
  }
  domain_names = {
    "internal.dev-api.tabspace.ai" = {
      # for backend alb
      domain_name = "internal.dev-api.tabspace.ai"
      alternative_names = []
    }
  }
}
