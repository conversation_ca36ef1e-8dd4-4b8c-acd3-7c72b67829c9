module "public_alb" {
  source              = "../../../../modules/aws/load_balancer/alb"
  account_name        = var.account_name
  environment         = local.env
  enable_route53_alb  = false
  internal            = false
  route53_zone        = ""
  security_group_name = "tabstudio-public-alb"
  security_group_rules = {
    ingress_http = {
      type      = "ingress"
      from_port = 80
      to_port   = 80
      protocol  = "tcp"
      cidr      = ["0.0.0.0/0"]
    }
    ingress_https = {
      type      = "ingress"
      from_port = 443
      to_port   = 443
      protocol  = "tcp"
      cidr      = ["0.0.0.0/0"]
    }
  }
  service_name = var.service_name
  vpc_id       = data.terraform_remote_state.vpc.outputs.vpc_id
  subnet_ids   = data.terraform_remote_state.public_subnets.outputs.public_subnet_ids
}

data "terraform_remote_state" "vpc" {
  backend = "s3"
  config = {
    bucket  = "tabstudio-terraform-state-${local.env}"
    key     = "L1/${local.env}/${local.region}/vpc/terraform.tfstate"
    region  = local.region
    encrypt = true
  }
}

data "terraform_remote_state" "public_subnets" {
  backend = "s3"
  config = {
    bucket  = "tabstudio-terraform-state-${local.env}"
    key     = "L1/${local.env}/${local.region}/subnet/terraform.tfstate"
    region  = local.region
    encrypt = true
  }
}
