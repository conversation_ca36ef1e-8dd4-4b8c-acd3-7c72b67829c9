output "public_alb_dns_name" {
  description = "The DNS name of the load balancer"
  value       = module.public_alb.dns_name
}

output "public_alb_arn" {
  description = "The ARN of the load balancer"
  value       = module.public_alb.lb_arn
}

output "public_alb_id" {
  description = "The ID of the load balancer"
  value       = module.public_alb.lb_id
}

output "public_alb_zone_id" {
  description = "The canonical hosted zone ID of the load balancer"
  value       = module.public_alb.zone_id
}

output "public_alb_target_group_arns" {
  description = "Map of target group names to their ARNs"
  value       = module.public_alb.target_group_arns
}

output "public_alb_target_group_names" {
  description = "Map of target group keys to their names"
  value       = module.public_alb.target_group_names
}

output "public_alb_listener_arns" {
  description = "Map of listener names to their ARNs"
  value       = module.public_alb.listener_arns
}

output "public_alb_certificate_arn" {
  description = "The ARN of the certificate being used"
  value       = module.public_alb.certificate_arn
}

output "public_alb_created_certificate_arn" {
  description = "The ARN of the ACM certificate created by this module (if any)"
  value       = module.public_alb.created_certificate_arn
}

output "public_alb_security_group_id" {
  description = "The ID of the security group attached to the load balancer"
  value       = module.public_alb.security_group_id
}
