# Multi-Cluster ECS Architecture Design

## Overview

This document outlines the architecture for supporting multiple ECS clusters within a single Terraform module, providing isolation, scalability, and maintainability.

## Architecture Principles

### 1. **Loose Coupling & Isolation**
- Each ECS cluster is completely isolated from others
- No shared resources between clusters that could cause conflicts
- Independent IAM roles, security groups, and networking per cluster
- Separate CloudWatch log groups for each cluster

### 2. **Submodule Structure**
- Main module orchestrates multiple cluster submodules
- Each cluster submodule is self-contained and reusable
- Clear separation of concerns between orchestration and implementation

### 3. **Hash-based Resource Naming**
- Use `for_each` instead of `count` for predictable resource addressing
- Consistent naming conventions prevent resource conflicts
- Stable resource names that don't change with configuration order

### 4. **User-friendly Configuration**
- Map-based input structure for intuitive cluster definition
- Sensible defaults to minimize required configuration
- Comprehensive validation to catch errors early

## Directory Structure

```
L1/dev/ap-southeast-5/ecs/
├── main.tf                    # Main orchestration module
├── variables.tf               # Multi-cluster input variables
├── outputs.tf                 # Structured outputs
├── locals.tf                  # Local values and logic
├── versions.tf                # Provider requirements
├── modules/
│   └── cluster/               # ECS cluster submodule
│       ├── main.tf            # Cluster resources
│       ├── variables.tf       # Cluster-specific variables
│       ├── outputs.tf         # Cluster outputs
│       └── versions.tf        # Provider requirements
├── examples/
│   ├── single-cluster/        # Single cluster example
│   ├── multi-cluster/         # Multiple clusters example
│   └── advanced/              # Advanced configuration example
└── README.md                  # Comprehensive documentation
```

## Data Flow Architecture

### Input Structure
```hcl
clusters = {
  "web-cluster" = {
    enable_container_insights = true
    capacity_providers = ["FARGATE", "FARGATE_SPOT"]
    default_capacity_provider_strategy = {
      FARGATE = { weight = 50, base = 20 }
      FARGATE_SPOT = { weight = 50 }
    }
    # ... other cluster-specific configuration
  }
  "api-cluster" = {
    # ... different configuration
  }
}
```

### Processing Flow
1. **Input Validation**: Validate cluster configurations and names
2. **Resource Creation**: Use `for_each` to create cluster submodules
3. **Isolation**: Each cluster gets unique resource names and configurations
4. **Output Aggregation**: Collect and structure outputs from all clusters

### Output Structure
```hcl
clusters = {
  "web-cluster" = {
    id = "cluster-id"
    arn = "cluster-arn"
    name = "cluster-name"
    iam_roles = { ... }
    # ... complete cluster information
  }
  "api-cluster" = { ... }
}
```

## Resource Isolation Strategy

### 1. **Naming Convention**
- Cluster names must be unique within the module
- All resources prefixed with cluster name
- Format: `{cluster-name}-{resource-type}-{suffix}`

### 2. **IAM Isolation**
- Separate execution and task roles per cluster
- Role names: `{cluster-name}-ecs-execution` and `{cluster-name}-ecs-task`
- No shared IAM resources between clusters

### 3. **CloudWatch Isolation**
- Separate log groups per cluster
- Log group names: `/aws/ecs/{cluster-name}`
- Independent retention and encryption settings

### 4. **Network Isolation**
- Clusters can be deployed in different VPCs/subnets
- No shared networking resources
- Independent security group configurations

## Scalability Considerations

### 1. **Horizontal Scaling**
- Easy addition of new clusters through configuration
- No limit on number of clusters (within AWS limits)
- Independent scaling of cluster resources

### 2. **Configuration Flexibility**
- Each cluster can have different capacity providers
- Independent Container Insights settings
- Flexible IAM role configurations

### 3. **State Management**
- Predictable resource addressing with `for_each`
- Stable Terraform state across changes
- Easy cluster addition/removal without affecting others

## Security Architecture

### 1. **Principle of Least Privilege**
- Each cluster has minimal required permissions
- Separate IAM roles prevent cross-cluster access
- Optional additional policies per cluster

### 2. **Encryption**
- Independent KMS key support per cluster
- CloudWatch log encryption per cluster
- No shared encryption resources

### 3. **Access Control**
- Cluster-specific ECS Exec configurations
- Independent logging and monitoring
- Separate audit trails per cluster

## Implementation Strategy

### Phase 1: Core Architecture
1. Create cluster submodule with all necessary resources
2. Implement main module with `for_each` orchestration
3. Design input/output variable structure

### Phase 2: Advanced Features
1. Add comprehensive validation rules
2. Implement advanced configuration options
3. Create structured output hierarchy

### Phase 3: Documentation & Examples
1. Create comprehensive examples
2. Write detailed documentation
3. Add migration guides

## Backward Compatibility

### Legacy Support
- Maintain support for single-cluster configuration
- Automatic migration path from old to new structure
- Clear deprecation warnings for old patterns

### Migration Strategy
- Provide migration examples
- Document state migration process
- Offer both old and new interfaces during transition

## Validation Rules

### Cluster Names
- Must be unique within the module
- Must follow AWS naming conventions
- Must start with letter, contain only alphanumeric, hyphens, underscores

### Configuration Validation
- Capacity provider validation
- Strategy weight/base validation
- IAM policy ARN validation
- CloudWatch retention period validation

## Error Handling

### Input Validation
- Early validation of all input parameters
- Clear error messages for configuration issues
- Helpful suggestions for common mistakes

### Resource Conflicts
- Unique naming prevents resource conflicts
- Clear error messages for naming collisions
- Validation of AWS resource limits

This architecture provides a robust, scalable, and maintainable solution for managing multiple ECS clusters while ensuring complete isolation and user-friendly configuration.
