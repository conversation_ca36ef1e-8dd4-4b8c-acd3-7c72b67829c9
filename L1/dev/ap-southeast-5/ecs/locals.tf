locals {
  project    = "tabstudio"
  env        = "dev"
  region     = "ap-southeast-5"
  team       = "infra"
  managed_by = "terraform"

  default_tags = {
    project   = local.project
    env       = local.env
    team      = local.team
    managedBy = local.managed_by
    region    = local.region
  }

  # Multi-cluster configuration logic
  name_prefix = var.name_prefix != "" ? "${var.name_prefix}-" : ""

  # Backward compatibility: convert legacy single cluster config to new format
  legacy_cluster = var.name != "" ? {
    (var.name) = {
      enable_container_insights           = var.enable_container_insights
      capacity_providers                  = var.capacity_providers
      default_capacity_provider_strategy = var.default_capacity_provider_strategy
      execute_command_configuration      = null
      create_cloudwatch_log_group        = true
      cloudwatch_log_group_retention_days = 7
      cloudwatch_log_group_kms_key_id     = null
      create_task_execution_role          = true
      task_execution_role_name            = null
      task_execution_role_policies        = []
      create_task_role                    = true
      task_role_name                      = null
      task_role_policies                  = []
      cluster_tags                        = {}
    }
  } : {}

  # Merge legacy and new cluster configurations
  all_clusters = merge(local.legacy_cluster, var.clusters)

  # Validate that we have at least one cluster defined
  has_clusters = length(local.all_clusters) > 0

  # Global tags to apply to all resources
  global_tags = merge(local.default_tags, var.tags)

  # Generate cluster-specific configurations with merged tags
  cluster_configs = {
    for cluster_name, cluster_config in local.all_clusters : cluster_name => merge(cluster_config, {
      # Add name prefix if specified
      effective_name = "${local.name_prefix}${cluster_name}"

      # Merge global and cluster-specific tags
      merged_tags = merge(local.global_tags, cluster_config.cluster_tags, {
        ClusterName = cluster_name
      })
    })
  }
}
