# Multi-Cluster ECS Module - Main Configuration

# ------------------------------
# Validation
# ------------------------------
resource "null_resource" "validate_clusters" {
  count = local.has_clusters ? 0 : 1

  provisioner "local-exec" {
    command = "echo 'Error: At least one cluster must be defined in the clusters variable or legacy name variable' && exit 1"
  }
}

# ------------------------------
# ECS Clusters (Multi-cluster support using for_each)
# ------------------------------
module "ecs_clusters" {
  source = "./modules/cluster"

  for_each = local.cluster_configs

  # Basic cluster configuration
  name                      = each.value.effective_name
  enable_container_insights = each.value.enable_container_insights

  # Capacity providers configuration
  capacity_providers                 = each.value.capacity_providers
  default_capacity_provider_strategy = each.value.default_capacity_provider_strategy

  # ECS Exec configuration
  execute_command_configuration = each.value.execute_command_configuration

  # CloudWatch configuration
  create_cloudwatch_log_group         = each.value.create_cloudwatch_log_group
  cloudwatch_log_group_retention_days = each.value.cloudwatch_log_group_retention_days
  cloudwatch_log_group_kms_key_id     = each.value.cloudwatch_log_group_kms_key_id

  # IAM configuration
  create_task_execution_role   = each.value.create_task_execution_role
  task_execution_role_name     = each.value.task_execution_role_name
  task_execution_role_policies = each.value.task_execution_role_policies
  create_task_role             = each.value.create_task_role
  task_role_name               = each.value.task_role_name
  task_role_policies           = each.value.task_role_policies

  # Tags
  tags         = each.value.merged_tags
  cluster_tags = each.value.cluster_tags
}

# ------------------------------
# Legacy Support Resources (for backward compatibility)
# ------------------------------

# Legacy single cluster outputs (when using deprecated variables)
locals {
  # Get the first cluster for legacy compatibility
  legacy_cluster_key = var.name != "" ? var.name : ""
  has_legacy_cluster = local.legacy_cluster_key != ""
}
