# ECS Cluster Submodule - Main Resources

# Local values for resource naming and configuration
locals {
  cluster_name = var.name
  
  # CloudWatch log group name
  log_group_name = var.execute_command_configuration != null && var.execute_command_configuration.log_configuration != null && var.execute_command_configuration.log_configuration.cloud_watch_log_group_name != null ? var.execute_command_configuration.log_configuration.cloud_watch_log_group_name : "/aws/ecs/${local.cluster_name}"
  
  # IAM role names
  execution_role_name = var.task_execution_role_name != null ? var.task_execution_role_name : "${local.cluster_name}-ecs-execution"
  task_role_name      = var.task_role_name != null ? var.task_role_name : "${local.cluster_name}-ecs-task"
  
  # Merge tags
  common_tags = merge(var.tags, {
    Cluster = local.cluster_name
  })
}

# ------------------------------
# ECS Cluster
# ------------------------------
resource "aws_ecs_cluster" "this" {
  name = local.cluster_name

  setting {
    name  = "containerInsights"
    value = var.enable_container_insights ? "enabled" : "disabled"
  }

  dynamic "configuration" {
    for_each = var.execute_command_configuration != null ? [var.execute_command_configuration] : []
    
    content {
      execute_command_configuration {
        logging    = configuration.value.logging
        kms_key_id = configuration.value.kms_key_id

        dynamic "log_configuration" {
          for_each = configuration.value.log_configuration != null ? [configuration.value.log_configuration] : []
          
          content {
            cloud_watch_log_group_name     = log_configuration.value.cloud_watch_log_group_name != null ? log_configuration.value.cloud_watch_log_group_name : local.log_group_name
            cloud_watch_encryption_enabled = log_configuration.value.cloud_watch_encryption_enabled
            s3_bucket_name                 = log_configuration.value.s3_bucket_name
            s3_bucket_encryption_enabled   = log_configuration.value.s3_bucket_encryption_enabled
            s3_key_prefix                  = log_configuration.value.s3_key_prefix
          }
        }
      }
    }
  }

  tags = merge(local.common_tags, var.cluster_tags)
}

# ------------------------------
# CloudWatch Log Group for ECS Exec
# ------------------------------
resource "aws_cloudwatch_log_group" "this" {
  count = var.create_cloudwatch_log_group ? 1 : 0

  name              = local.log_group_name
  retention_in_days = var.cloudwatch_log_group_retention_days
  kms_key_id        = var.cloudwatch_log_group_kms_key_id

  tags = merge(local.common_tags, {
    Name = local.log_group_name
  })
}

# ------------------------------
# ECS Cluster Capacity Providers
# ------------------------------
resource "aws_ecs_cluster_capacity_providers" "this" {
  cluster_name       = aws_ecs_cluster.this.name
  capacity_providers = var.capacity_providers

  dynamic "default_capacity_provider_strategy" {
    for_each = var.default_capacity_provider_strategy
    
    content {
      base              = default_capacity_provider_strategy.value.base
      capacity_provider = default_capacity_provider_strategy.key
      weight            = default_capacity_provider_strategy.value.weight
    }
  }
}

# ------------------------------
# Task Execution Role
# ------------------------------
resource "aws_iam_role" "execution" {
  count = var.create_task_execution_role ? 1 : 0
  
  name               = local.execution_role_name
  assume_role_policy = data.aws_iam_policy_document.execution_assume_role.json

  tags = merge(local.common_tags, {
    Name = local.execution_role_name
    Type = "TaskExecutionRole"
  })
}

data "aws_iam_policy_document" "execution_assume_role" {
  statement {
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
    actions = ["sts:AssumeRole"]
  }
}

# Attach the default ECS task execution role policy
resource "aws_iam_role_policy_attachment" "execution_default" {
  count = var.create_task_execution_role ? 1 : 0
  
  role       = aws_iam_role.execution[0].name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Attach additional policies to execution role
resource "aws_iam_role_policy_attachment" "execution_additional" {
  for_each = var.create_task_execution_role ? toset(var.task_execution_role_policies) : toset([])
  
  role       = aws_iam_role.execution[0].name
  policy_arn = each.value
}

# ------------------------------
# Task Role
# ------------------------------
resource "aws_iam_role" "task" {
  count = var.create_task_role ? 1 : 0
  
  name               = local.task_role_name
  assume_role_policy = data.aws_iam_policy_document.task_assume_role.json

  tags = merge(local.common_tags, {
    Name = local.task_role_name
    Type = "TaskRole"
  })
}

data "aws_iam_policy_document" "task_assume_role" {
  statement {
    effect = "Allow"
    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
    actions = ["sts:AssumeRole"]
  }
}

# Attach additional policies to task role
resource "aws_iam_role_policy_attachment" "task_additional" {
  for_each = var.create_task_role ? toset(var.task_role_policies) : toset([])
  
  role       = aws_iam_role.task[0].name
  policy_arn = each.value
}
