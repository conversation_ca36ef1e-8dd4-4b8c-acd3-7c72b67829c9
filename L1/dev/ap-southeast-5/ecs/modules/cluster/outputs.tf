# ECS Cluster Submodule - Outputs

# ------------------------------
# Cluster Outputs
# ------------------------------
output "cluster_id" {
  description = "ID of the ECS cluster"
  value       = aws_ecs_cluster.this.id
}

output "cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = aws_ecs_cluster.this.arn
}

output "cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.this.name
}

output "cluster_capacity_providers" {
  description = "Map of cluster capacity providers"
  value       = aws_ecs_cluster_capacity_providers.this.capacity_providers
}

output "cluster_default_capacity_provider_strategy" {
  description = "Default capacity provider strategy for the cluster"
  value       = aws_ecs_cluster_capacity_providers.this.default_capacity_provider_strategy
}

# ------------------------------
# CloudWatch Log Group Outputs
# ------------------------------
output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = var.create_cloudwatch_log_group ? aws_cloudwatch_log_group.this[0].name : null
}

output "cloudwatch_log_group_arn" {
  description = "ARN of the CloudWatch log group"
  value       = var.create_cloudwatch_log_group ? aws_cloudwatch_log_group.this[0].arn : null
}

# ------------------------------
# IAM Role Outputs
# ------------------------------
output "task_execution_role_arn" {
  description = "ARN of the task execution role"
  value       = var.create_task_execution_role ? aws_iam_role.execution[0].arn : null
}

output "task_execution_role_name" {
  description = "Name of the task execution role"
  value       = var.create_task_execution_role ? aws_iam_role.execution[0].name : null
}

output "task_role_arn" {
  description = "ARN of the task role"
  value       = var.create_task_role ? aws_iam_role.task[0].arn : null
}

output "task_role_name" {
  description = "Name of the task role"
  value       = var.create_task_role ? aws_iam_role.task[0].name : null
}

# ------------------------------
# Comprehensive Cluster Information
# ------------------------------
output "cluster_info" {
  description = "Comprehensive information about the ECS cluster"
  value = {
    id                   = aws_ecs_cluster.this.id
    arn                  = aws_ecs_cluster.this.arn
    name                 = aws_ecs_cluster.this.name
    capacity_providers   = aws_ecs_cluster_capacity_providers.this.capacity_providers
    container_insights   = var.enable_container_insights
    
    # CloudWatch information
    cloudwatch_log_group = var.create_cloudwatch_log_group ? {
      name = aws_cloudwatch_log_group.this[0].name
      arn  = aws_cloudwatch_log_group.this[0].arn
    } : null
    
    # IAM roles information
    iam_roles = {
      task_execution = var.create_task_execution_role ? {
        arn  = aws_iam_role.execution[0].arn
        name = aws_iam_role.execution[0].name
      } : null
      
      task = var.create_task_role ? {
        arn  = aws_iam_role.task[0].arn
        name = aws_iam_role.task[0].name
      } : null
    }
  }
}
