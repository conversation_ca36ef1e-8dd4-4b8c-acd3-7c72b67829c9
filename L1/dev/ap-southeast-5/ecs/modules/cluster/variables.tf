# ECS Cluster Submodule Variables

variable "name" {
  description = "Name of the ECS cluster"
  type        = string
  
  validation {
    condition     = can(regex("^[a-zA-Z][a-zA-Z0-9-_]*$", var.name))
    error_message = "Cluster name must start with a letter and contain only alphanumeric characters, hyphens, and underscores."
  }
}

variable "enable_container_insights" {
  description = "Enable CloudWatch Container Insights for the cluster"
  type        = bool
  default     = true
}

variable "capacity_providers" {
  description = "List of capacity providers for the ECS cluster"
  type        = list(string)
  default     = ["FARGATE", "FARGATE_SPOT"]
  
  validation {
    condition = alltrue([
      for provider in var.capacity_providers : 
      contains(["FARGATE", "FARGATE_SPOT", "EC2"], provider)
    ])
    error_message = "Capacity providers must be one of: FARGATE, FARGATE_SPOT, EC2."
  }
}

variable "default_capacity_provider_strategy" {
  description = "Default capacity provider strategy for the cluster"
  type = map(object({
    base   = optional(number, 0)
    weight = optional(number, 1)
  }))
  default = {}
  
  validation {
    condition = alltrue([
      for strategy in var.default_capacity_provider_strategy :
      strategy.base >= 0 && strategy.weight >= 0
    ])
    error_message = "Base and weight values must be non-negative."
  }
}

variable "execute_command_configuration" {
  description = "Configuration for ECS Exec command execution"
  type = object({
    logging = optional(string, "DEFAULT")
    log_configuration = optional(object({
      cloud_watch_log_group_name     = optional(string)
      cloud_watch_encryption_enabled = optional(bool, false)
      s3_bucket_name                 = optional(string)
      s3_bucket_encryption_enabled   = optional(bool, false)
      s3_key_prefix                  = optional(string)
    }))
    kms_key_id = optional(string)
  })
  default = null
}

variable "create_cloudwatch_log_group" {
  description = "Whether to create a CloudWatch log group for ECS Exec"
  type        = bool
  default     = true
}

variable "cloudwatch_log_group_retention_days" {
  description = "Number of days to retain CloudWatch logs"
  type        = number
  default     = 7
  
  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1096, 1827, 2192, 2557, 2922, 3288, 3653
    ], var.cloudwatch_log_group_retention_days)
    error_message = "CloudWatch log group retention days must be a valid value."
  }
}

variable "cloudwatch_log_group_kms_key_id" {
  description = "KMS key ID for CloudWatch log group encryption"
  type        = string
  default     = null
}

variable "create_task_execution_role" {
  description = "Whether to create a task execution role for this cluster"
  type        = bool
  default     = true
}

variable "task_execution_role_name" {
  description = "Name for the task execution role (if created)"
  type        = string
  default     = null
}

variable "task_execution_role_policies" {
  description = "Additional policies to attach to the task execution role"
  type        = list(string)
  default     = []
}

variable "create_task_role" {
  description = "Whether to create a base task role for this cluster"
  type        = bool
  default     = true
}

variable "task_role_name" {
  description = "Name for the task role (if created)"
  type        = string
  default     = null
}

variable "task_role_policies" {
  description = "Additional policies to attach to the task role"
  type        = list(string)
  default     = []
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "cluster_tags" {
  description = "Additional tags to apply specifically to the ECS cluster"
  type        = map(string)
  default     = {}
}
