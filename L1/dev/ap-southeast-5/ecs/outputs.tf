
# Multi-Cluster ECS Module - Outputs

# ------------------------------
# Multi-Cluster Outputs
# ------------------------------
output "clusters" {
  description = "Map of all ECS clusters with their complete information"
  value = {
    for cluster_name, cluster_module in module.ecs_clusters : cluster_name => cluster_module.cluster_info
  }
}

output "cluster_arns" {
  description = "Map of cluster names to their ARNs"
  value = {
    for cluster_name, cluster_module in module.ecs_clusters : cluster_name => cluster_module.cluster_arn
  }
}

output "cluster_ids" {
  description = "Map of cluster names to their IDs"
  value = {
    for cluster_name, cluster_module in module.ecs_clusters : cluster_name => cluster_module.cluster_id
  }
}

output "cluster_names" {
  description = "Map of cluster keys to their actual names (including prefix)"
  value = {
    for cluster_name, cluster_module in module.ecs_clusters : cluster_name => cluster_module.cluster_name
  }
}

output "task_execution_role_arns" {
  description = "Map of cluster names to their task execution role ARNs"
  value = {
    for cluster_name, cluster_module in module.ecs_clusters : cluster_name => cluster_module.task_execution_role_arn
  }
}

output "task_role_arns" {
  description = "Map of cluster names to their task role ARNs"
  value = {
    for cluster_name, cluster_module in module.ecs_clusters : cluster_name => cluster_module.task_role_arn
  }
}

output "cloudwatch_log_group_names" {
  description = "Map of cluster names to their CloudWatch log group names"
  value = {
    for cluster_name, cluster_module in module.ecs_clusters : cluster_name => cluster_module.cloudwatch_log_group_name
  }
}

# ------------------------------
# Convenience Outputs
# ------------------------------
output "cluster_count" {
  description = "Total number of ECS clusters created"
  value       = length(module.ecs_clusters)
}

output "cluster_list" {
  description = "List of all cluster names"
  value       = keys(module.ecs_clusters)
}

# ------------------------------
# Legacy Outputs (Backward Compatibility)
# ------------------------------
output "id" {
  description = "[DEPRECATED] Use clusters output instead. ID of the first cluster for backward compatibility"
  value       = local.has_legacy_cluster && length(module.ecs_clusters) > 0 ? values(module.ecs_clusters)[0].cluster_id : null
}

output "arn" {
  description = "[DEPRECATED] Use clusters output instead. ARN of the first cluster for backward compatibility"
  value       = local.has_legacy_cluster && length(module.ecs_clusters) > 0 ? values(module.ecs_clusters)[0].cluster_arn : null
}

output "name" {
  description = "[DEPRECATED] Use clusters output instead. Name of the first cluster for backward compatibility"
  value       = local.has_legacy_cluster && length(module.ecs_clusters) > 0 ? values(module.ecs_clusters)[0].cluster_name : null
}

output "execution_role_arn" {
  description = "[DEPRECATED] Use task_execution_role_arns output instead. Execution role ARN of the first cluster"
  value       = local.has_legacy_cluster && length(module.ecs_clusters) > 0 ? values(module.ecs_clusters)[0].task_execution_role_arn : null
}

output "task_role_arn" {
  description = "[DEPRECATED] Use task_role_arns output instead. Task role ARN of the first cluster"
  value       = local.has_legacy_cluster && length(module.ecs_clusters) > 0 ? values(module.ecs_clusters)[0].task_role_arn : null
}