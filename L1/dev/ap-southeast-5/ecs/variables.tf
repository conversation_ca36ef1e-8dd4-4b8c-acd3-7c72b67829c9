# modules/ecs-cluster/main.tf
variable "name" {
  type        = string
  description = "ECS Cluster name"
}

variable "capacity_providers" {
  type        = list(string)
  default     = ["FARGATE", "FARGATE_SPOT"]
  description = "Capacity providers for ECS cluster"
}

variable "default_capacity_provider_strategy" {
  type = map(object({
    base   = optional(number)
    weight = optional(number)
  }))
  default = {}
}

variable "tags" {
  type    = map(string)
  default = {}
}


variable "enable_container_insights" {
  type    = bool
  default = true
}

