# Multi-Cluster ECS Module Variables

# ------------------------------
# Multi-Cluster Configuration
# ------------------------------
variable "clusters" {
  description = "Map of ECS clusters to create with their configurations"
  type = map(object({
    # Basic cluster configuration
    enable_container_insights = optional(bool, true)

    # Capacity providers configuration
    capacity_providers = optional(list(string), ["FARGATE", "FARGATE_SPOT"])
    default_capacity_provider_strategy = optional(map(object({
      base   = optional(number, 0)
      weight = optional(number, 1)
    })), {})

    # ECS Exec configuration
    execute_command_configuration = optional(object({
      logging = optional(string, "DEFAULT")
      log_configuration = optional(object({
        cloud_watch_log_group_name     = optional(string)
        cloud_watch_encryption_enabled = optional(bool, false)
        s3_bucket_name                 = optional(string)
        s3_bucket_encryption_enabled   = optional(bool, false)
        s3_key_prefix                  = optional(string)
      }))
      kms_key_id = optional(string)
    }))

    # CloudWatch configuration
    create_cloudwatch_log_group        = optional(bool, true)
    cloudwatch_log_group_retention_days = optional(number, 7)
    cloudwatch_log_group_kms_key_id     = optional(string)

    # IAM configuration
    create_task_execution_role     = optional(bool, true)
    task_execution_role_name       = optional(string)
    task_execution_role_policies   = optional(list(string), [])
    create_task_role               = optional(bool, true)
    task_role_name                 = optional(string)
    task_role_policies             = optional(list(string), [])

    # Tags
    cluster_tags = optional(map(string), {})
  }))
  default = {}

  validation {
    condition = alltrue([
      for cluster_name, cluster_config in var.clusters :
      can(regex("^[a-zA-Z][a-zA-Z0-9-_]*$", cluster_name))
    ])
    error_message = "All cluster names must start with a letter and contain only alphanumeric characters, hyphens, and underscores."
  }

  validation {
    condition = alltrue([
      for cluster_name, cluster_config in var.clusters :
      alltrue([
        for provider in cluster_config.capacity_providers :
        contains(["FARGATE", "FARGATE_SPOT", "EC2"], provider)
      ])
    ])
    error_message = "All capacity providers must be one of: FARGATE, FARGATE_SPOT, EC2."
  }

  validation {
    condition = alltrue([
      for cluster_name, cluster_config in var.clusters :
      contains([1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1096, 1827, 2192, 2557, 2922, 3288, 3653], cluster_config.cloudwatch_log_group_retention_days)
    ])
    error_message = "CloudWatch log group retention days must be a valid value."
  }
}

# ------------------------------
# Global Configuration
# ------------------------------
variable "tags" {
  description = "Global tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "name_prefix" {
  description = "Prefix to add to all resource names for additional uniqueness"
  type        = string
  default     = ""

  validation {
    condition     = var.name_prefix == "" || can(regex("^[a-zA-Z][a-zA-Z0-9-]*$", var.name_prefix))
    error_message = "Name prefix must start with a letter and contain only alphanumeric characters and hyphens."
  }
}

# ------------------------------
# Legacy Support (Backward Compatibility)
# ------------------------------
variable "name" {
  description = "[DEPRECATED] Use clusters variable instead. Single cluster name for backward compatibility"
  type        = string
  default     = ""
}

variable "capacity_providers" {
  description = "[DEPRECATED] Use clusters variable instead. Capacity providers for single cluster"
  type        = list(string)
  default     = ["FARGATE", "FARGATE_SPOT"]
}

variable "default_capacity_provider_strategy" {
  description = "[DEPRECATED] Use clusters variable instead. Default capacity provider strategy for single cluster"
  type = map(object({
    base   = optional(number)
    weight = optional(number)
  }))
  default = {}
}

variable "enable_container_insights" {
  description = "[DEPRECATED] Use clusters variable instead. Enable container insights for single cluster"
  type        = bool
  default     = true
}

