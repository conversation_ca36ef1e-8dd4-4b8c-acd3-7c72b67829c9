
# Data source to get available AZs (for validation)
data "aws_availability_zones" "available" {
  state = "available"
}

data "terraform_remote_state" "vpc" {
  backend = "s3"
  config = {
    bucket = "tabstudio-terraform-state-${local.env}"
    key    = "L1/${local.env}/${local.region}/vpc/terraform.tfstate"
    region = local.region
    encrypt = true
  }
}

# Public Subnets
resource "aws_subnet" "public" {
  count = 3

  vpc_id                  = data.terraform_remote_state.vpc.outputs.vpc_id
  cidr_block              = var.public_subnet_cidrs[count.index]
  availability_zone       = var.availability_zones[count.index]
  map_public_ip_on_launch = true

  tags = {
    name = "${local.project}-public-subnet-${count.index + 1}-${local.env}"
    Name = "${local.project}-public-subnet-${count.index + 1}-${local.env}"
    type = "public"
    az   = var.availability_zones[count.index]
  }
}

# Private Subnets
resource "aws_subnet" "private" {
  count = 3

  vpc_id            = data.terraform_remote_state.vpc.outputs.vpc_id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]

  tags = {
    name = "${local.project}-private-subnet-${count.index + 1}-${local.env}"
    Name = "${local.project}-private-subnet-${count.index + 1}-${local.env}"
    type = "private"
    az   = var.availability_zones[count.index]
  }
}