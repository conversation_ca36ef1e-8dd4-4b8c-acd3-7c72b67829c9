# VPC Information
output "vpc_id" {
  description = "The ID of the VPC"
  value       = data.terraform_remote_state.vpc.outputs.vpc_id
}

# Public Subnets
output "public_subnet_ids" {
  description = "List of IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "public_subnet_cidrs" {
  description = "List of CIDR blocks of the public subnets"
  value       = aws_subnet.public[*].cidr_block
}

output "public_subnet_arns" {
  description = "List of ARNs of the public subnets"
  value       = aws_subnet.public[*].arn
}

output "public_subnets_by_az" {
  description = "Map of availability zone to public subnet ID"
  value = {
    for idx, subnet in aws_subnet.public : var.availability_zones[idx] => subnet.id
  }
}

# Private Subnets
output "private_subnet_ids" {
  description = "List of IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

output "private_subnet_cidrs" {
  description = "List of CIDR blocks of the private subnets"
  value       = aws_subnet.private[*].cidr_block
}

output "private_subnet_arns" {
  description = "List of ARNs of the private subnets"
  value       = aws_subnet.private[*].arn
}

output "private_subnets_by_az" {
  description = "Map of availability zone to private subnet ID"
  value = {
    for idx, subnet in aws_subnet.private : var.availability_zones[idx] => subnet.id
  }
}

# Availability Zones
output "availability_zones" {
  description = "List of availability zones used"
  value       = var.availability_zones
}

# Summary Information
output "subnet_summary" {
  description = "Summary of all subnets created"
  value = {
    public_subnets = {
      for idx, subnet in aws_subnet.public : 
      "subnet-${idx + 1}" => {
        id         = subnet.id
        cidr_block = subnet.cidr_block
        az         = subnet.availability_zone
      }
    }
    private_subnets = {
      for idx, subnet in aws_subnet.private : 
      "subnet-${idx + 1}" => {
        id         = subnet.id
        cidr_block = subnet.cidr_block
        az         = subnet.availability_zone
      }
    }
  }
}
