variable "availability_zones" {
  description = "List of availability zones to create subnets in"
  type        = list(string)
  default     = ["ap-southeast-5a", "ap-southeast-5b", "ap-southeast-5c"]
  
  validation {
    condition     = length(var.availability_zones) == 3
    error_message = "Exactly 3 availability zones must be specified."
  }
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for public subnets"
  type        = list(string)
  default     = ["********/24", "********/24", "********/24"]
  
  validation {
    condition     = length(var.public_subnet_cidrs) == 3
    error_message = "Exactly 3 public subnet CIDR blocks must be specified."
  }
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for private subnets"
  type        = list(string)
  default     = ["*********/24", "*********/24", "*********/24"]
  
  validation {
    condition     = length(var.private_subnet_cidrs) == 3
    error_message = "Exactly 3 private subnet CIDR blocks must be specified."
  }
}

variable "account_id" {
  type = string
}