## VPC Routing

### Overview
- This stack creates and wires up Internet egress for a VPC by managing the Internet Gateway (IGW), NAT Gateways, and Route Tables.
- It consumes VPC and Subnet information from remote states and configures routing for public and private subnets.
- The focus is north-south internet connectivity (ingress for public subnets via IGW, egress for private subnets via NAT). No east-west (TGW) or VPC endpoints are created here.

#### Adopted network model
- Public subnets share a single public Route Table:
  - 0.0.0.0/0 -> Internet Gateway (IGW)
  - Associated to all public subnets
- Private subnets use per-AZ private Route Tables:
  - Each AZ has its own private Route Table
  - Each private subnet is associated to the Route Table of its AZ
  - 0.0.0.0/0 -> NAT Gateway, with the NAT placement depending on the selected strategy (see below)
- Internet Gateway: a single IGW is attached to the VPC
- NAT Gateways: number and placement are determined by nat_strategy

#### NAT strategies
- single (default):
  - One NAT Gateway is created in the first public subnet (first AZ)
  - All private AZ route tables send 0.0.0.0/0 to this single NAT (cross-AZ egress)
  - Lowest cost, but not AZ-resilient; cross-AZ data charges may apply
- per_az:
  - One NAT Gateway per Availability Zone, placed in that AZ’s public subnet
  - Each private AZ route table points to its local AZ NAT Gateway
  - Higher cost, improved AZ resilience, no cross-AZ egress for private subnets

#### Dependencies (remote state reads)
- L1/dev/ap-southeast-5/vpc
  - Provides vpc_id
- L1/dev/ap-southeast-5/subnet
  - Provides public_subnet_ids, public_subnets_by_az, private_subnets_by_az, and a subnet summary used to map subnets to AZs

#### Outputs (selected)
- nat_allocation: Map showing which AZ/subnet received a NAT allocation
- nat_gateway_ids, nat_gateway_public_ips, nat_gateway_subnet_ids, nat_gateway_allocation_ids
- route_table_ids, route_table_arns (private route tables)
- route_table_association_* (private associations)
- route_ids (0.0.0.0/0 routes from private route tables)

#### Operational notes
- Changing nat_strategy from single to per_az (or vice versa) will recreate NAT Gateways and modify private route tables/associations; plan carefully to avoid downtime.
- The set of AZs and subnets comes from the subnet module. Ensure that module is applied first and remains consistent.
- Cost considerations: per_az creates one NAT per AZ; single uses one NAT total.

