data "terraform_remote_state" "vpc" {
  backend = "s3"
  config = {
    bucket = "tabstudio-terraform-state-${local.env}"
    key    = "L1/${local.env}/${local.region}/vpc/terraform.tfstate"
    region = local.region
    encrypt = true
  }
}

data "terraform_remote_state" "subnet" {
  backend = "s3"
  config = {
    bucket = "tabstudio-terraform-state-${local.env}"
    key    = "L1/${local.env}/${local.region}/subnet/terraform.tfstate"
    region = local.region
    encrypt = true
  }
}
