locals {
  project    = "tabstudio"
  env        = "dev"
  region     = "ap-southeast-5"
  team       = "infra"
  managed_by = "terraform"
  default_tags = {
    project   = local.project
    env       = local.env
    team      = local.team
    managedBy = local.managed_by
    region    = local.region
  }
  name_prefix = "${local.project}-${local.env}"

  strategy          = var.nat_strategy
  vpc_id            = data.terraform_remote_state.vpc.outputs.vpc_id
  public_subnet_ids = data.terraform_remote_state.subnet.outputs.public_subnet_ids

  public_subnet_to_az = {
    for az, subnet_id in data.terraform_remote_state.subnet.outputs.public_subnets_by_az :
    subnet_id => az
  }

  private_az_to_subnet = data.terraform_remote_state.subnet.outputs.private_subnets_by_az
  private_subnet_to_az = {
    for name, subnet in data.terraform_remote_state.subnet.outputs.subnet_summary["private_subnets"] :
    subnet["id"] => subnet["az"]
  }

  nat_allocation = (
    local.strategy == "per_az" ?
    { for sid, az in local.public_subnet_to_az : az => sid } :
    local.strategy == "single" ?
    { values(local.public_subnet_to_az)[0] = keys(local.public_subnet_to_az)[0] } :
    {}
  )
}
