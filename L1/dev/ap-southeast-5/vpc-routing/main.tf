# Internet Gateway
resource "aws_internet_gateway" "igw" {
  vpc_id = local.vpc_id
}

# Elastic IPs for NAT(s)
resource "aws_eip" "nat_eip" {
  for_each = local.nat_allocation
  domain   = "vpc"
  tags = {
    Name = "${local.name_prefix}-nat-eip-${each.key}"
  }
}

# NAT Gateway(s)
resource "aws_nat_gateway" "nat" {
  for_each      = local.nat_allocation
  allocation_id = aws_eip.nat_eip[each.key].id
  subnet_id     = each.value
  depends_on    = [aws_internet_gateway.igw]
  tags = {
    Name = "${local.name_prefix}-nat-${each.key}"
  }
}

# Public Route Table
resource "aws_route_table" "public" {
  vpc_id = local.vpc_id
  tags = {
    Name = "${local.name_prefix}-public-route-table"
  }
}

resource "aws_route" "public_internet" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.igw.id
}

resource "aws_route_table_association" "public_route_assoc" {
  for_each       = toset(local.public_subnet_ids)
  subnet_id      = each.key
  route_table_id = aws_route_table.public.id
}

# Private Route Table(s)
resource "aws_route_table" "private" {
  for_each = local.private_az_to_subnet
  vpc_id   = local.vpc_id
  tags = {
    Name = "${local.name_prefix}-private-route-table-${each.key}"
  }
}

resource "aws_route" "private_nat" {
  for_each               = local.private_az_to_subnet
  route_table_id         = aws_route_table.private[each.key].id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = length(local.nat_allocation) == 1 ? aws_nat_gateway.nat[keys(local.nat_allocation)[0]].id : aws_nat_gateway.nat[aws_nat_gateway.nat[each.key]].id
}

resource "aws_route_table_association" "private_assoc" {
  for_each       = local.private_subnet_to_az
  subnet_id      = each.key
  route_table_id = aws_route_table.private[each.value].id
}
