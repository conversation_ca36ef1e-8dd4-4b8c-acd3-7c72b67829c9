output "nat_allocation" {
  description = "Map of NAT allocation"
  value       = local.nat_allocation
}

output "nat_gateway_ids" {
  description = "Map of NAT gateway IDs"
  value       = { for k, v in aws_nat_gateway.nat : k => v.id }
}

output "nat_gateway_public_ips" {
  description = "Map of NAT gateway public IPs"
  value       = { for k, v in aws_nat_gateway.nat : k => v.public_ip }
}

output "nat_gateway_subnet_ids" {
  description = "Map of NAT gateway subnet IDs"
  value       = { for k, v in aws_nat_gateway.nat : k => v.subnet_id }
}

output "nat_gateway_allocation_ids" {
  description = "Map of NAT gateway allocation IDs"
  value       = { for k, v in aws_nat_gateway.nat : k => v.allocation_id }
}

output "route_table_ids" {
  description = "Map of route table IDs"
  value       = { for k, v in aws_route_table.private : k => v.id }
}

output "route_table_arns" {
  description = "Map of route table ARNs"
  value       = { for k, v in aws_route_table.private : k => v.arn }
}

output "route_table_association_ids" {
  description = "Map of route table association IDs"
  value       = { for k, v in aws_route_table_association.private_assoc : k => v.id }
}

output "route_table_association_subnet_ids" {
  description = "Map of route table association subnet IDs"
  value       = { for k, v in aws_route_table_association.private_assoc : k => v.subnet_id }
}

output "route_table_association_route_table_ids" {
  description = "Map of route table association route table IDs"
  value       = { for k, v in aws_route_table_association.private_assoc : k => v.route_table_id }
}

output "route_ids" {
  description = "Map of route IDs"
  value       = { for k, v in aws_route.private_nat : k => v.id }
}
