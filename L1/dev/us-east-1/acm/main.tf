resource "aws_acm_certificate" "tabstudio" {
  for_each                  = local.domain_names
  domain_name               = each.value.domain_name
  subject_alternative_names = each.value.alternative_names
  validation_method         = "DNS"
  tags = {
    Name = each.value.domain_name
  }
}

data "aws_route53_zone" "tabstudio_route53_zone" {
  name = var.base_zone_name
}

resource "aws_route53_record" "tabstudio_route53_validation_record" {
  for_each = {
    for i, pair in flatten([
      for name, _ in local.domain_names : [
        for dvo in aws_acm_certificate.tabstudio[name].domain_validation_options : {
          domain_name           = dvo.domain_name
          resource_record_name  = dvo.resource_record_name
          resource_record_type  = dvo.resource_record_type
          resource_record_value = dvo.resource_record_value
        } if !startswith(dvo.domain_name, "*") # wildcard domain will have the same validation record as the base domain
      ]
      ]) : pair.domain_name => {
      resource_record_name  = pair.resource_record_name
      resource_record_type  = pair.resource_record_type
      resource_record_value = pair.resource_record_value
    }
  }
  zone_id = data.aws_route53_zone.tabstudio_route53_zone.id
  name    = each.value.resource_record_name
  type    = each.value.resource_record_type
  ttl     = 60
  records = [each.value.resource_record_value]
}

resource "aws_acm_certificate_validation" "tabstudio_validation" {
  for_each        = aws_acm_certificate.tabstudio
  certificate_arn = aws_acm_certificate.tabstudio[each.key].arn
  validation_record_fqdns = [for domain in toset(compact(concat([each.value.domain_name], tolist(each.value.subject_alternative_names)))) :
    aws_route53_record.tabstudio_route53_validation_record[domain].fqdn if !startswith(domain, "*")]
}
