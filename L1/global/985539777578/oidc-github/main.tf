# Safety check to ensure we're in the right AWS account
data "aws_caller_identity" "current" {}

locals {
  expected_account_id = local.account_id
}

resource "null_resource" "check_account" {
  provisioner "local-exec" {
    command = <<EOT
if [ "${local.account_id}" != "${data.aws_caller_identity.current.account_id}" ]; then
  echo "ERROR: This OIDC provider is intended for AWS account ${local.expected_account_id}, but you are logged into ${data.aws_caller_identity.current.account_id}."
  exit 1
fi
EOT
  }
}

# GitHub Actions OIDC provider
resource "aws_iam_openid_connect_provider" "github" {
  url             = "https://token.actions.githubusercontent.com"
  client_id_list  = ["sts.amazonaws.com"]

  depends_on = [null_resource.check_account]
  tags = {
    Name = "GitHub Actions OIDC Provider"
  }
}


# Optional OIDC test role
resource "aws_iam_role" "oidc_test_role" {
  count = var.create_test_role ? 1 : 0

  name = "oidc-github-test-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Federated = aws_iam_openid_connect_provider.github.arn
        }
        Action = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          StringEquals = {
            "token.actions.githubusercontent.com:aud" = "sts.amazonaws.com"
          }
          StringLike = {
            "token.actions.githubusercontent.com:sub" = "repo:${local.test_repo_organisation}/*"
          }
        }
      }
    ]
  })
}

# Inline policy for test role
resource "aws_iam_role_policy" "oidc_test_inline" {
  count = var.create_test_role ? 1 : 0

  name = "oidc-github-test-inline-policy"
  role = aws_iam_role.oidc_test_role[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "sts:GetCallerIdentity"
        Resource = "*"
      }
    ]
  })
}
