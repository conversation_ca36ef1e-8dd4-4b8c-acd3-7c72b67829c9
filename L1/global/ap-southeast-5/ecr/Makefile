.PHONY: all init plan apply refresh output clean

current_dir = $(shell pwd)
assume-role:
	@( \
		echo "current_dir: $(current_dir)" && \
		echo "run the following env var setup in your console" && \
		cd ../../../.. && source .venv/bin/activate && python3 tools/bootstrap-tf.py --profile $(profile) --region ap-southeast-5 --env dev --action assume-role \
    )

init:
	@( \
		echo "Initializing Terraform..." && \
		terraform init \
 	)

plan:
	terraform plan -var-file=terraform.tfvars --var-file=local.tfvars -out tf.plan -lock=false -detailed-exitcode

apply:
	terraform apply tf.plan
	rm tf.plan

refresh:
	terraform refresh -var-file=terraform.tfvars --var-file=local.tfvars

output:
	terraform output

clean:
	rm -rf tf.plan .terraform/terraform.tfstate

destroy:
	terraform destroy -var-file=terraform.tfvars --var-file=local.tfvars
