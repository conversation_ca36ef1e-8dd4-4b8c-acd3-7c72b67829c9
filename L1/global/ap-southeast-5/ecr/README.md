ECR Repositories (ap-southeast-5)

Overview
- This stack manages one or more AWS ECR repositories in account ************, region ap-southeast-5.
- It enables image scan on push and applies a lifecycle policy to expire untagged images after 30 days.
- Outputs include repository ARN, URL, and name.

Add or update an ECR repository
1. Open locals.tf in this directory.
2. Edit the local.repositories list and add a new object with the repository name. Example:
   repositories = [{
     name = "tabstudio-backend"
   },{
     name = "another-service"
   }]
3. Plan and apply
4. Inspect outputs for the new repositories:
   - make output
   The output ecr_repositories maps each repo name to its arn, url and name.

What is created per repository
- aws_ecr_repository with image_tag_mutability = MUTABLE
- image_scanning_configuration.scan_on_push = true
- aws_ecr_lifecycle_policy that expires untagged images after 30 days

This module makes no assumption on the use case of the ECR repository. However, following are the common use cases which might interest you.

### Grant GitHub Actions runner permissions to push/pull images
This repository does not attach a repository policy; instead, permissions are granted via an IAM role used by GitHub Actions (OIDC).

How to allow the runner to access your new ECR repo
1. Get the ECR repository ARN from the ECR stack output (or construct it as arn:aws:ecr:ap-southeast-5:************:repository/<repo_name>).
2. In L1/dev/global/github-backend-runner/terraform.tfvars, add the repo to the ecr_repos list:
   ecr_repos = [{
     name = "tabstudio-backend"
     arn  = "arn:aws:ecr:ap-southeast-5:************:repository/tabstudio-backend"
   },{
     name = "another-service"
     arn  = "arn:aws:ecr:ap-southeast-5:************:repository/another-service"
   }]
3. Plan and apply the stack.
4. Output github_actions_backend_role will contain the role name and ARN to use in GitHub Actions.

Depending on how you are going to test the permission, in most cases, you only need the Github role arn for your setup.

Notes
- Ensure you add each ECR repository ARN you need into github-backend-runner/terraform.tfvars so the role has permissions to push to those repos.
- If you change the environment or account, update locals.tf and backend.tf accordingly in each stack path.
