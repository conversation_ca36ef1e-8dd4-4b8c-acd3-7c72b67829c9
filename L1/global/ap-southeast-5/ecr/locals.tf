locals {
  project    = "tabstudio"
  env        = "global"
  region     = "ap-southeast-5"
  team       = "infra"
  managed_by = "terraform"
  account_id = "************"
  default_tags = {
    project   = local.project
    env       = local.env
    team      = local.team
    managedBy = local.managed_by
    region    = local.region
  }
  # this structure is extensible to support cross account permission grant
  repositories = [{
    name = "tabstudio-backend"
    # example for future use
    # principals = [{
    #   name = "github-actions-backend-dev"
    #   arn  = "arn:aws:iam::************:role/github-actions-backend-dev"
    #   actions = [
    #     "ecr:BatchCheckLayerAvailability",
    #     "ecr:GetAuthorizationToken",
    #     "ecr:PutImage",
    #     "ecr:InitiateLayerUpload",
    #     "ecr:UploadLayerPart",
    #     "ecr:CompleteLayerUpload",
    #     "ecr:BatchGetImage"
    #   ]
    #   env = "dev"
    },{
    name = "tabstudio-app"
  }]
  repositories_dict = {
    for repo in local.repositories : repo.name => repo
  }
}
