# Create multiple ECR repositories
resource "aws_ecr_repository" "repos" {
  for_each = toset([for repo in local.repositories : repo.name])

  name                 = local.repositories_dict[each.key].name
  image_tag_mutability = "MUTABLE"

  # Optional: scan on push
  image_scanning_configuration {
    scan_on_push = true
  }
  tags = {
    Name = local.repositories_dict[each.key].name
  }
}

resource "aws_ecr_lifecycle_policy" "repo_lifecycles" {
  for_each   = aws_ecr_repository.repos
  repository = each.value.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Expire untagged images after 30 days"
        selection = {
          tagStatus   = "untagged"
          countType   = "sinceImagePushed"
          countUnit   = "days"
          countNumber = 30
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

