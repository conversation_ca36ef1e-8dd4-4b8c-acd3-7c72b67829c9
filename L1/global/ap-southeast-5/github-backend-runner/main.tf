data "terraform_remote_state" "github-oidc" {
  backend = "s3"
  config = {
    bucket = "tabstudio-terraform-state-dev"
    key    = "L1/global/${local.account_id}/oidc-github/terraform.tfstate"
    region = local.region
    encrypt = true
  }
}

# GitHub Actions IAM Role for backend deployment
resource "aws_iam_role" "github_actions_backend" {
  name = "github-actions-backend"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Federated = data.terraform_remote_state.github-oidc.outputs.oidc_provider_arn
        }
        Action = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          StringEquals = {
            "token.actions.githubusercontent.com:aud" = "sts.amazonaws.com"
          }
          StringLike = {
            "token.actions.githubusercontent.com:sub" = "repo:${local.github_org}/*"
          }
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "github_actions_basic_policy" {
  name = "github-actions-backend-basic-policy"
  role = aws_iam_role.github_actions_backend.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "sts:GetCallerIdentity"
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy" "github_actions_ecr_policy" {
  name = "github-actions-backend-ecr-policy"
  role = aws_iam_role.github_actions_backend.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "ecr:GetAuthorizationToken"
        Resource = "*"
      },
      {
        Effect   = "Allow"
        Action   = [
          "ecr:BatchCheckLayerAvailability",
          "ecr:PutImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:BatchGetImage",
          "ecr:ListImages"
        ]
        Resource = [
          for repo in var.ecr_repos : repo.arn
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy" "github_actions_secrets_policy" {
  name = "github-actions-backend-secrets-policy"
  role = aws_iam_role.github_actions_backend.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "secretsmanager:GetSecretValue"
        Resource = var.secrets_manager_secrets
      }
    ]
  })
}