provider "aws" {
  region              = "ap-southeast-5"
  allowed_account_ids = [local.account_id]
  # assume_role {
  #   role_arn = "arn:aws:iam::${var.account_id}:role/terraform-${local.env}"
  # }
  default_tags {
    tags = local.default_tags
  }
}

terraform {
  required_version = ">= 1.2"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">= 6.8.0"
    }
  }
}
