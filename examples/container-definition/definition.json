{"command": ["/usr/sbin/apache2", "-D", "FOREGROUND"], "cpu": 512, "dependsOn": [{"condition": "START", "containerName": "fluent-bit"}], "disableNetworking": false, "dnsSearchDomains": ["mydns.on.my.network"], "dnsServers": ["***********"], "dockerLabels": {"com.example.label": "value"}, "dockerSecurityOptions": ["no-new-privileges"], "entrypoint": ["/usr/sbin/apache2", "-D", "FOREGROUND"], "environment": [{"name": "ENV_VAR_1", "value": "value1"}, {"name": "ENV_VAR_2", "value": "value2"}], "environmentFiles": [{"type": "s3", "value": "s3://my-bucket/my-env-file.env"}], "essential": true, "firelensConfiguration": {"type": "fluentbit"}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:3000/health || exit 1"], "interval": 30, "retries": 3, "timeout": 5}, "image": "public.ecr.aws/aws-containers/ecsdemo-frontend:776fd50", "interactive": false, "linuxParameters": {"capabilities": {"add": [], "drop": ["NET_RAW"]}, "initProcessEnabled": false}, "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "firehose", "delivery_stream": "my-stream", "log-driver-buffer-limit": "2097152", "region": "eu-west-1"}}, "memory": 1024, "memoryReservation": 100, "mountPoints": [{"containerPath": "/var/www/my-vol", "readOnly": null, "sourceVolume": "my-vol"}, {"containerPath": "/ebs/data", "readOnly": null, "sourceVolume": "ebs-volume"}], "name": "ex-container-definition", "portMappings": [{"containerPort": 3000, "hostPort": 3000, "name": "ecsdemo-frontend", "protocol": "tcp"}], "privileged": false, "pseudoTerminal": false, "readonlyRootFilesystem": true, "repositoryCredentials": {"credentialsParameter": "arn:aws:secretsmanager:eu-west-1:123456789012:secret:my-repo-creds"}, "resourceRequirements": [{"type": "GPU", "value": "1"}], "restartPolicy": {"enabled": true, "ignoredExitCodes": [1], "restartAttemptPeriod": 60}, "secrets": [{"name": "SECRET_ENV_VAR", "valueFrom": "arn:aws:ssm:eu-west-1:123456789012:parameter/my-secret-env-var"}], "startTimeout": 30, "stopTimeout": 120, "systemControls": [{"namespace": "network", "value": "ipv6"}, {"namespace": "net.core.somaxconn", "value": "1024"}], "ulimits": [{"hardLimit": 2048, "name": "nofile", "softLimit": 1024}], "user": "65534", "versionConsistency": "disabled", "volumesFrom": [{"readOnly": false, "sourceContainer": "fluent-bit"}], "workingDirectory": "/var/www/html"}