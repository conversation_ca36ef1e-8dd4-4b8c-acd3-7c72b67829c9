# Enhanced AWS Application Load Balancer (ALB) Terraform Module

This enhanced Terraform module creates an Application Load Balancer (ALB) on AWS with advanced features including conditional SSL certificate management, multiple target groups, and multiple listener rules.

## 🚀 Enhanced Features

### 1. Conditional SSL Certificate Management
- **Self-managed certificates**: Provide an existing certificate ARN to skip ACM certificate creation
- **Auto-generated certificates**: Automatically create and validate ACM certificates with DNS validation
- **Smart detection**: Module automatically detects certificate configuration and skips unnecessary resources

### 2. Multiple Target Groups Support
- Create multiple target groups with different configurations
- Hash-based naming using `for_each` for predictable resource names
- Support for different target types (instance, IP, lambda)
- Individual health check configurations per target group

### 3. Multiple Listener Rules Support
- Configure multiple listeners (HTTP, HTTPS) with custom rules
- Support for various condition types: host-header, path-pattern, http-header, etc.
- Multiple actions: forward, redirect, fixed-response
- Proper priority ordering for rule evaluation

### 4. Hash-based Resource Naming
- All resources use `for_each` instead of `count` for stable resource addressing
- Predictable resource names that don't change with configuration order
- Better support for resource updates and modifications

## 📋 Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## 🔧 Basic Usage

### Using Existing Certificate

```hcl
module "alb" {
  source = "./modules/aws/load_balancer/alb"

  # Basic configuration
  environment    = "prod"
  account_name   = "mycompany"
  service_name   = "webapp"
  vpc_id         = "vpc-********"

  # Use existing certificate
  certificate_arn    = "arn:aws:acm:us-east-1:********9012:certificate/********-1234-1234-1234-********9012"
  create_certificate = false

  # Security group
  security_group_name = "webapp-alb"
  security_group_rules = {
    https_ingress = {
      type      = "ingress"
      from_port = 443
      to_port   = 443
      protocol  = "tcp"
      cidr      = ["0.0.0.0/0"]
    }
  }

  # Target groups
  target_groups = {
    web = {
      name        = "webapp-tg"
      port        = 80
      protocol    = "HTTP"
      target_type = "instance"
      targets = [
        {
          id   = "i-********90abcdef0"
          port = 80
        }
      ]
    }
  }

  # Listeners
  listeners = {
    https = {
      port     = 443
      protocol = "HTTPS"
      default_action = {
        type             = "forward"
        target_group_key = "web"
      }
    }
  }
}
```

### Creating New Certificate

```hcl
module "alb" {
  source = "./modules/aws/load_balancer/alb"

  # Basic configuration
  environment    = "prod"
  account_name   = "mycompany"
  service_name   = "webapp"
  vpc_id         = "vpc-********"

  # Create new certificate
  create_certificate = true
  cert_domain        = "webapp.example.com"
  route53_zone       = "example.com"

  # Security group and other configuration...
}
```

## 🎯 Advanced Usage

### Multiple Target Groups with Listener Rules

```hcl
module "alb_advanced" {
  source = "./modules/aws/load_balancer/alb"

  environment    = "prod"
  account_name   = "mycompany"
  service_name   = "microservices"
  vpc_id         = var.vpc_id
  certificate_arn = var.certificate_arn

  # Multiple target groups for different services
  target_groups = {
    frontend = {
      name                = "frontend-tg"
      port                = 80
      protocol            = "HTTP"
      target_type         = "instance"
      health_check_path   = "/health"
      health_check_matcher = "200"
      targets = [
        { id = "i-frontend1", port = 80 },
        { id = "i-frontend2", port = 80 }
      ]
    }

    api = {
      name                = "api-tg"
      port                = 8080
      protocol            = "HTTP"
      target_type         = "instance"
      health_check_path   = "/api/health"
      health_check_matcher = "200,202"
      targets = [
        { id = "i-api1", port = 8080 },
        { id = "i-api2", port = 8080 }
      ]
    }

    admin = {
      name                = "admin-tg"
      port                = 3000
      protocol            = "HTTP"
      target_type         = "instance"
      health_check_path   = "/admin/health"
      targets = [
        { id = "i-admin1", port = 3000 }
      ]
    }
  }

  # HTTPS listener with multiple rules
  listeners = {
    https = {
      port     = 443
      protocol = "HTTPS"
      ssl_policy = "ELBSecurityPolicy-TLS13-1-2-2021-06"

      # Default action
      default_action = {
        type             = "forward"
        target_group_key = "frontend"
      }

      # Multiple listener rules
      rules = [
        {
          priority = 100
          conditions = [
            {
              field  = "path-pattern"
              values = ["/api/*"]
            }
          ]
          actions = [
            {
              type             = "forward"
              target_group_key = "api"
            }
          ]
        },
        {
          priority = 200
          conditions = [
            {
              field  = "host-header"
              values = ["admin.example.com"]
            }
          ]
          actions = [
            {
              type             = "forward"
              target_group_key = "admin"
            }
          ]
        },
        {
          priority = 300
          conditions = [
            {
              field  = "path-pattern"
              values = ["/legacy/*"]
            }
          ]
          actions = [
            {
              type = "redirect"
              redirect = {
                protocol    = "HTTPS"
                port        = "443"
                host        = "new.example.com"
                path        = "/#{path}"
                status_code = "HTTP_301"
              }
            }
          ]
        }
      ]
    }

    # HTTP listener with redirect to HTTPS
    http = {
      port     = 80
      protocol = "HTTP"

      default_action = {
        type = "redirect"
        redirect = {
          protocol    = "HTTPS"
          port        = "443"
          status_code = "HTTP_301"
        }
      }
    }
  }

  # Security group configuration
  security_group_name = "microservices-alb"
  security_group_rules = {
    http_ingress = {
      type      = "ingress"
      from_port = 80
      to_port   = 80
      protocol  = "tcp"
      cidr      = ["0.0.0.0/0"]
    }
    https_ingress = {
      type      = "ingress"
      from_port = 443
      to_port   = 443
      protocol  = "tcp"
      cidr      = ["0.0.0.0/0"]
    }
    egress_all = {
      type      = "egress"
      from_port = 0
      to_port   = 65535
      protocol  = "tcp"
      cidr      = ["0.0.0.0/0"]
    }
  }

  tags_basic = {
    Environment = "prod"
    Project     = "microservices"
  }
}
```

## 📝 Input Variables

### SSL Certificate Configuration

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| `certificate_arn` | ARN of existing SSL certificate. If provided, ACM certificate creation will be skipped | `string` | `""` | no |
| `create_certificate` | Whether to create a new ACM certificate | `bool` | `true` | no |
| `cert_domain` | Domain name for certificate creation (only used when creating new certificate) | `string` | `""` | no |
| `route53_zone` | Route53 zone name for DNS validation (only used when creating new certificate) | `string` | `""` | no |

### Target Groups Configuration

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| `target_groups` | Map of target groups to create | `map(object)` | `{}` | no |

Target group object structure:
```hcl
{
  name                          = string
  port                          = number
  protocol                      = string
  target_type                   = string
  health_check_port             = optional(string, "traffic-port")
  health_check_protocol         = optional(string, "HTTP")
  health_check_path             = optional(string, "/")
  health_check_matcher          = optional(string, "200")
  healthy_threshold             = optional(number, 2)
  unhealthy_threshold           = optional(number, 2)
  health_check_interval         = optional(number, 30)
  health_check_timeout          = optional(number, 5)
  deregistration_delay          = optional(number, 300)
  load_balancing_algorithm_type = optional(string, "round_robin")
  targets = optional(list(object({
    id                = string
    port              = optional(number)
    availability_zone = optional(string)
  })), [])
}
```

### Listeners Configuration

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| `listeners` | Map of listeners to create | `map(object)` | `{}` | no |

### Legacy Variables (Backward Compatibility)

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| `tg_name` | [DEPRECATED] Use target_groups instead | `string` | `""` | no |
| `listener_port` | [DEPRECATED] Use listeners instead | `string` | `"443"` | no |
| `enable_http_listener` | [DEPRECATED] Use listeners instead | `bool` | `false` | no |

## 📤 Outputs

| Name | Description |
|------|-------------|
| `lb_arn` | The ARN of the load balancer |
| `dns_name` | The DNS name of the load balancer |
| `target_group_arns` | Map of target group names to their ARNs |
| `listener_arns` | Map of listener names to their ARNs |
| `certificate_arn` | The ARN of the certificate being used |
| `created_certificate_arn` | The ARN of the ACM certificate created by this module (if any) |
| `security_group_id` | The ID of the security group attached to the load balancer |

## 🔄 Migration Guide

### From Legacy Configuration

If you're using the legacy single target group configuration:

**Before:**
```hcl
module "alb" {
  source = "./modules/aws/load_balancer/alb"

  tg_name               = "my-app-tg"
  tg_port               = 80
  tg_protocol           = "HTTP"
  listener_port         = "443"
  listener_protocol     = "HTTPS"
  enable_http_listener  = true
  cert_domain           = "app.example.com"
  route53_zone          = "example.com"
}
```

**After:**
```hcl
module "alb" {
  source = "./modules/aws/load_balancer/alb"

  # Certificate configuration
  create_certificate = true
  cert_domain        = "app.example.com"
  route53_zone       = "example.com"

  # Target groups
  target_groups = {
    app = {
      name        = "my-app-tg"
      port        = 80
      protocol    = "HTTP"
      target_type = "instance"
    }
  }

  # Listeners
  listeners = {
    https = {
      port     = 443
      protocol = "HTTPS"
      default_action = {
        type             = "forward"
        target_group_key = "app"
      }
    }
    http = {
      port     = 80
      protocol = "HTTP"
      default_action = {
        type = "redirect"
        redirect = {
          protocol    = "HTTPS"
          port        = "443"
          status_code = "HTTP_301"
        }
      }
    }
  }
}
```

### Backward Compatibility

The module maintains backward compatibility with legacy variables. Existing configurations will continue to work without changes, but new features require the new variable structure.

## 🧪 Testing

See the `examples/comprehensive-test/` directory for a complete test example that demonstrates all features.

## 📚 Examples

- [Comprehensive Test](examples/comprehensive-test/) - Complete example with all features
- [Basic Usage](examples/basic/) - Simple ALB setup
- [Multiple Services](examples/microservices/) - Multiple target groups and rules

## 🐛 Troubleshooting

### Certificate Issues
- Ensure Route53 zone exists and is accessible
- Verify domain name matches the hosted zone
- Check DNS propagation for validation records

### Target Group Health Checks
- Verify security group rules allow health check traffic
- Confirm health check path returns expected status codes
- Check target instance accessibility

### Listener Rule Conflicts
- Ensure rule priorities don't conflict (lower numbers = higher priority)
- Verify condition patterns match expected traffic
- Test rule evaluation order
```