resource "aws_lb" "alb" {
  name                             = "${var.environment}-${var.service_name}-alb"
  internal                         = var.internal
  load_balancer_type               = "application"
  enable_deletion_protection       = var.enable_deletion_protection
  enable_cross_zone_load_balancing = var.enable_cross_zone_load_balancing
  subnets                          = data.aws_subnets.subnets.ids
  security_groups                  = [module.load_balancer_sg[0].security_group_id]

  tags = (merge(var.tags_basic, var.tags_extra, ))
}


# ACM Certificate (only created when not using existing certificate)
resource "aws_acm_certificate" "acm_certificate" {
  count = local.should_create_certificate ? 1 : 0

  domain_name       = var.cert_domain
  validation_method = "DNS"

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(var.tags_basic, var.tags_extra, {
    Name = "${var.environment}-${var.service_name}-certificate"
  })
}

# Route53 DNS validation records (only created when creating new certificate)
resource "aws_route53_record" "route53_record" {
  for_each = local.should_create_certificate ? {
    for dvo in aws_acm_certificate.acm_certificate[0].domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  } : {}

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = "CNAME"
  zone_id         = data.aws_route53_zone.route53_zone[0].zone_id
}

# Certificate validation (only when creating new certificate)
resource "aws_acm_certificate_validation" "acm_certificate_validation" {
  count = local.should_create_certificate ? 1 : 0

  certificate_arn         = aws_acm_certificate.acm_certificate[0].arn
  validation_record_fqdns = [for record in aws_route53_record.route53_record : record.fqdn]

  timeouts {
    create = "5m"
  }
}

# ALB DNS record (optional)
resource "aws_route53_record" "alb_dns_record" {
  count = var.enable_route53_alb && var.route53_zone != "" ? 1 : 0

  zone_id = data.aws_route53_zone.route53_zone[0].zone_id
  name    = "${var.service_name}.${var.route53_zone}"
  type    = "CNAME"
  ttl     = 300
  records = [aws_lb.alb.dns_name]
}

resource "aws_acm_certificate_validation" "acm_certificate_validation" {
  certificate_arn         = aws_acm_certificate.acm_certificate.arn
  validation_record_fqdns = [for record in aws_route53_record.route53_record : record.fqdn]
  depends_on = [ aws_acm_certificate.acm_certificate, aws_route53_record.route53_record ]
}