resource "aws_lb" "alb" {
  name                             = "${var.environment}-${var.service_name}-alb"
  internal                         = var.internal
  load_balancer_type               = "application"
  enable_deletion_protection       = var.enable_deletion_protection
  enable_cross_zone_load_balancing = var.enable_cross_zone_load_balancing
  subnets                          = data.aws_subnets.subnets.ids
  security_groups                  = [module.load_balancer_sg[0].security_group_id]

  tags = (merge(var.tags_basic, var.tags_extra, ))
}


resource "aws_acm_certificate" "acm_certificate" {
  domain_name       = var.cert_domain
  validation_method = "DNS"

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_route53_record" "route53_record" {
  for_each = {
    for dvo in aws_acm_certificate.acm_certificate.domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  }

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = "CNAME"
  zone_id         = data.aws_route53_zone.route53_zone.zone_id
}

resource "aws_route53_record" "alb_dns_record" {
  zone_id = data.aws_route53_zone.route53_zone.zone_id
  name    = "${var.service_name}.${var.route53_zone}"
  type    = "CNAME"
  ttl     = 300
  records = [aws_lb.alb.dns_name]
}

resource "aws_acm_certificate_validation" "acm_certificate_validation" {
  certificate_arn         = aws_acm_certificate.acm_certificate.arn
  validation_record_fqdns = [for record in aws_route53_record.route53_record : record.fqdn]
  depends_on = [ aws_acm_certificate.acm_certificate, aws_route53_record.route53_record ]
}