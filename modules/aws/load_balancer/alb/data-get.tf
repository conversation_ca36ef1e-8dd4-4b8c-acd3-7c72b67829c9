data "aws_vpc" "selected" {
  id = var.vpc_id
}

data "aws_subnets" "subnets" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.selected.id]
  }
}

data "aws_caller_identity" "current" {}
data "aws_iam_policy_document" "s3_policy" {
  statement {
    sid     = "S3-policy-service-access"
    actions = ["s3:*Object", "s3:ListBucket"]
    resources = [
      join(":", ["arn", "aws", "s3", ":", local.access_logs_bucket_name]),
      join(":", ["arn", "aws", "s3", ":", "${local.access_logs_bucket_name}/*"])
    ]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
      ]
    }
  }

  statement {
    sid     = "ALB-PutObject-policy-service-access"
    actions = ["s3:PutObject"]
    resources = [
      join(":", ["arn", "aws", "s3", ":", local.access_logs_bucket_name]),
      join(":", ["arn", "aws", "s3", ":", "${local.access_logs_bucket_name}/*"])
    ]
    principals {
      type = "AWS"
      identifiers = [
        "arn:aws:iam::************:root"
      ]
    }
  }
}

data "aws_route53_zone" "route53_zone" {
  name = var.route53_zone
}