# ALB Module Comprehensive Test Example

This example demonstrates all the enhanced features of the ALB module:

1. **Conditional SSL Certificate Management**
2. **Multiple Target Groups Support**
3. **Multiple Listener Rules Support**
4. **Hash-based Resource Naming (for_each instead of count)**

## Features Demonstrated

### 1. Conditional SSL Certificate Management

The example shows two scenarios:

- **Using Existing Certificate**: When `certificate_arn` is provided, the module skips ACM certificate creation and DNS validation
- **Creating New Certificate**: When `certificate_arn` is empty, the module creates a new ACM certificate with DNS validation

### 2. Multiple Target Groups

The example creates multiple target groups:
- `web`: For web application traffic on port 80
- `api`: For API traffic on port 8080

### 3. Multiple Listener Rules

The HTTPS listener includes multiple rules with different conditions:
- **Path-based routing**: Routes `/api/*` requests to the API target group
- **Host-based routing**: Redirects admin subdomain to a new location
- **Default action**: Routes all other traffic to the web target group

### 4. Hash-based Resource Naming

All resources use `for_each` instead of `count` for predictable, hash-based naming.

## Prerequisites

1. **AWS Provider**: Version ~> 5.0
2. **VPC**: An existing VPC with public subnets
3. **Certificate** (optional): An existing ACM certificate ARN
4. **Route53 Zone** (if creating certificate): A Route53 hosted zone for DNS validation

## Usage

### Step 1: Copy Configuration Files

```bash
# Copy the example terraform.tfvars file
cp terraform.tfvars.example terraform.tfvars
```

### Step 2: Update Variables

Edit `terraform.tfvars` with your values:

```hcl
# Required
vpc_id = "vpc-your-vpc-id"

# Optional - provide existing certificate ARN or leave empty to create new
certificate_arn = "arn:aws:acm:region:account:certificate/cert-id"
# OR leave empty to create new certificate:
# certificate_arn = ""

# For new certificate creation (only used if certificate_arn is empty)
domain_name  = "your-domain.com"
route53_zone = "your-domain.com"

environment = "test"
```

### Step 3: Initialize and Apply

```bash
# Initialize Terraform
terraform init

# Plan the deployment
terraform plan

# Apply the configuration
terraform apply
```

## Test Scenarios

### Scenario 1: Using Existing Certificate

Set `certificate_arn` to an existing certificate ARN:

```hcl
certificate_arn = "arn:aws:acm:us-east-1:************:certificate/********-1234-1234-1234-************"
```

**Expected Behavior:**
- No ACM certificate resources created
- No Route53 DNS validation records created
- ALB uses the provided certificate
- All other features work normally

### Scenario 2: Creating New Certificate

Leave `certificate_arn` empty:

```hcl
certificate_arn = ""
domain_name     = "test.example.com"
route53_zone    = "example.com"
```

**Expected Behavior:**
- New ACM certificate created for `test.example.com`
- Route53 DNS validation records created
- Certificate validation completed
- ALB uses the newly created certificate

## Testing the Features

### 1. Test Multiple Target Groups

```bash
# Check target groups created
terraform output alb_existing_cert_target_groups
```

### 2. Test Listener Rules

The HTTPS listener should have:
- Default action forwarding to web target group
- Rule 100: Forward `/api/*` to API target group
- Rule 200: Redirect admin subdomain

### 3. Test Certificate Management

```bash
# Check which certificate is being used
terraform output alb_existing_cert_certificate_arn

# If creating new certificate, check the created certificate
terraform output alb_new_cert_certificate_arn
```

## Cleanup

```bash
terraform destroy
```

## Outputs

The example provides comprehensive outputs:

- ALB ARNs and DNS names
- Target group ARNs
- Listener ARNs
- Certificate ARNs (both provided and created)
- Test configuration summary

## Troubleshooting

### Certificate Validation Issues

If certificate validation fails:
1. Ensure the Route53 zone exists and is properly configured
2. Check that the domain name matches the zone
3. Verify DNS propagation

### Target Group Health Checks

If targets are unhealthy:
1. Check security group rules allow health check traffic
2. Verify target instances are running and accessible
3. Confirm health check path returns expected status codes

### Listener Rule Conflicts

If listener rules don't work as expected:
1. Check rule priorities (lower numbers have higher priority)
2. Verify condition values match your test traffic
3. Ensure target groups exist and are healthy

## Advanced Configuration

You can extend this example by:

1. Adding more target groups
2. Creating additional listener rules
3. Using different condition types (query-string, source-ip, etc.)
4. Adding fixed-response actions
5. Configuring multiple certificates for different listeners
