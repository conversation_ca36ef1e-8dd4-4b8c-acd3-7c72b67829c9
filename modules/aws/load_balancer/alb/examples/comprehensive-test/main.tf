# Comprehensive ALB Module Test Example
# This example demonstrates all enhanced features:
# 1. Self-managed SSL certificates
# 2. Multiple target groups
# 3. Multiple listeners with rules
# 4. Hash-based resource naming

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Variables for the test
variable "vpc_id" {
  description = "VPC ID where the ALB will be created"
  type        = string
}

variable "certificate_arn" {
  description = "Existing certificate ARN (optional - leave empty to create new certificate)"
  type        = string
  default     = ""
}

variable "domain_name" {
  description = "Domain name for certificate creation (only used if certificate_arn is empty)"
  type        = string
  default     = "test.example.com"
}

variable "route53_zone" {
  description = "Route53 zone name (only used if creating certificate)"
  type        = string
  default     = "example.com"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "test"
}

# Data sources
data "aws_vpc" "selected" {
  id = var.vpc_id
}

data "aws_subnets" "public" {
  filter {
    name   = "vpc-id"
    values = [var.vpc_id]
  }
  
  tags = {
    Type = "public"
  }
}

# Example 1: ALB with self-managed certificate
module "alb_with_existing_cert" {
  source = "../../"

  # Basic ALB configuration
  environment    = var.environment
  account_name   = "tabspace"
  service_name   = "tabstudio"
  vpc_id         = var.vpc_id
  internal       = false

  # SSL Certificate - Using existing certificate
  certificate_arn   = var.certificate_arn
  create_certificate = false
  cert_domain       = "" # Not needed when using existing certificate
  route53_zone      = "" # Not needed when using existing certificate

  # Security Group
  security_group_name = "alb-existing-cert"
  security_group_rules = {
    ingress_http = {
      type        = "ingress"
      from_port   = 80
      to_port     = 80
      protocol    = "tcp"
      cidr        = ["0.0.0.0/0"]
    }
    ingress_https = {
      type        = "ingress"
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      cidr        = ["0.0.0.0/0"]
    }
    egress_all = {
      type        = "egress"
      from_port   = 0
      to_port     = 65535
      protocol    = "tcp"
      cidr        = ["0.0.0.0/0"]
    }
  }

  # Multiple Target Groups
  target_groups = {
    web = {
      name                = "${var.environment}-web-tg"
      port                = 80
      protocol            = "HTTP"
      target_type         = "instance"
      health_check_path   = "/health"
      health_check_matcher = "200"
      targets = [
        # Add your target instances here
        # {
        #   id   = "i-1234567890abcdef0"
        #   port = 80
        # }
      ]
    }
    api = {
      name                = "${var.environment}-api-tg"
      port                = 8080
      protocol            = "HTTP"
      target_type         = "instance"
      health_check_path   = "/api/health"
      health_check_matcher = "200,202"
      targets = [
        # Add your target instances here
        # {
        #   id   = "i-abcdef1234567890"
        #   port = 8080
        # }
      ]
    }
  }

  # Multiple Listeners with Rules
  listeners = {
    https = {
      port     = 443
      protocol = "HTTPS"
      ssl_policy = "ELBSecurityPolicy-TLS13-1-2-2021-06"
      
      # Default action - forward to web target group
      default_action = {
        type             = "forward"
        target_group_key = "web"
      }
      
      # Listener Rules
      rules = [
        {
          priority = 100
          conditions = [
            {
              field  = "path-pattern"
              values = ["/api/*"]
            }
          ]
          actions = [
            {
              type             = "forward"
              target_group_key = "api"
            }
          ]
        },
        {
          priority = 200
          conditions = [
            {
              field  = "host-header"
              values = ["admin.${var.domain_name}"]
            }
          ]
          actions = [
            {
              type = "redirect"
              redirect = {
                protocol    = "HTTPS"
                port        = "443"
                host        = "admin-new.${var.domain_name}"
                status_code = "HTTP_301"
              }
            }
          ]
        }
      ]
    }
    
    http = {
      port     = 80
      protocol = "HTTP"
      
      # Default action - redirect to HTTPS
      default_action = {
        type = "redirect"
        redirect = {
          protocol    = "HTTPS"
          port        = "443"
          status_code = "HTTP_301"
        }
      }
      
      rules = []
    }
  }

  # Tags
  tags_basic = {
    Environment = var.environment
    Project     = "alb-test"
    ManagedBy   = "terraform"
  }
  
  tags_extra = {
    TestType = "existing-certificate"
  }

  # Disable Route53 record creation for this test
  enable_route53_alb = false
}

# Example 2: ALB with auto-generated certificate (only if no existing certificate provided)
module "alb_with_new_cert" {
  count = var.certificate_arn == "" ? 1 : 0

  source = "../../"

  # Basic ALB configuration
  environment    = var.environment
  account_name   = "test"
  service_name   = "app-new-cert"
  vpc_id         = var.vpc_id
  internal       = false

  # SSL Certificate - Create new certificate
  certificate_arn    = ""
  create_certificate = true
  cert_domain        = var.domain_name
  route53_zone       = var.route53_zone

  # Security Group
  security_group_name = "alb-new-cert"
  security_group_rules = {
    ingress_https = {
      type        = "ingress"
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      cidr        = ["0.0.0.0/0"]
    }
    egress_all = {
      type        = "egress"
      from_port   = 0
      to_port     = 65535
      protocol    = "tcp"
      cidr        = ["0.0.0.0/0"]
    }
  }

  # Single Target Group
  target_groups = {
    app = {
      name                = "${var.environment}-app-new-cert-tg"
      port                = 80
      protocol            = "HTTP"
      target_type         = "instance"
      health_check_path   = "/"
      targets             = []
    }
  }

  # Simple HTTPS Listener
  listeners = {
    https = {
      port     = 443
      protocol = "HTTPS"

      default_action = {
        type             = "forward"
        target_group_key = "app"
      }

      rules = []
    }
  }

  # Tags
  tags_basic = {
    Environment = var.environment
    Project     = "alb-test"
    ManagedBy   = "terraform"
  }

  tags_extra = {
    TestType = "new-certificate"
  }

  # Enable Route53 record creation
  enable_route53_alb = true
}
