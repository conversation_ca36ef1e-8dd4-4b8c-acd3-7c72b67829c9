# Outputs for ALB with existing certificate
output "alb_existing_cert_arn" {
  description = "ARN of the ALB using existing certificate"
  value       = module.alb_with_existing_cert.lb_arn
}

output "alb_existing_cert_dns_name" {
  description = "DNS name of the ALB using existing certificate"
  value       = module.alb_with_existing_cert.dns_name
}

output "alb_existing_cert_target_groups" {
  description = "Target groups for ALB with existing certificate"
  value       = module.alb_with_existing_cert.target_group_arns
}

output "alb_existing_cert_listeners" {
  description = "Listeners for ALB with existing certificate"
  value       = module.alb_with_existing_cert.listener_arns
}

output "alb_existing_cert_certificate_arn" {
  description = "Certificate ARN used by ALB with existing certificate"
  value       = module.alb_with_existing_cert.certificate_arn
}

# Outputs for ALB with new certificate (conditional)
output "alb_new_cert_arn" {
  description = "ARN of the ALB using new certificate"
  value       = var.certificate_arn == "" ? module.alb_with_new_cert[0].lb_arn : null
}

output "alb_new_cert_dns_name" {
  description = "DNS name of the ALB using new certificate"
  value       = var.certificate_arn == "" ? module.alb_with_new_cert[0].dns_name : null
}

output "alb_new_cert_target_groups" {
  description = "Target groups for ALB with new certificate"
  value       = var.certificate_arn == "" ? module.alb_with_new_cert[0].target_group_arns : null
}

output "alb_new_cert_listeners" {
  description = "Listeners for ALB with new certificate"
  value       = var.certificate_arn == "" ? module.alb_with_new_cert[0].listener_arns : null
}

output "alb_new_cert_certificate_arn" {
  description = "Certificate ARN created for ALB with new certificate"
  value       = var.certificate_arn == "" ? module.alb_with_new_cert[0].created_certificate_arn : null
}

# Test summary
output "test_summary" {
  description = "Summary of the test configuration"
  value = {
    using_existing_certificate = var.certificate_arn != ""
    creating_new_certificate   = var.certificate_arn == ""
    domain_name               = var.domain_name
    route53_zone              = var.route53_zone
    vpc_id                    = var.vpc_id
    environment               = var.environment
  }
}
