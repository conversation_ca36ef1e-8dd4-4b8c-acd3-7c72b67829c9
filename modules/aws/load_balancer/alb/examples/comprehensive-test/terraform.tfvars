# Example terraform.tfvars file for ALB module testing
# Copy this file to terraform.tfvars and update the values

# Required: VPC ID where the ALB will be created
vpc_id = "vpc-c189d8a5"

# Optional: Existing certificate ARN
# If provided, the module will use this certificate and skip ACM certificate creation
# If empty, the module will create a new ACM certificate using cert_domain and route53_zone
certificate_arn = "arn:aws:acm:ap-southeast-1:749234582416:certificate/12d61a36-ecf0-490e-a330-79f8530328ec"
# certificate_arn = "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"

# Domain configuration (only used when creating new certificate)
# domain_name  = ""
# route53_zone = "example.com"

# Environment
environment = "dev"
