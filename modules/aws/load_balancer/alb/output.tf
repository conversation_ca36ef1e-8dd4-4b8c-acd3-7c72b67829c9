# ALB Outputs
output "lb_arn" {
  description = "The ARN of the load balancer"
  value       = aws_lb.alb.arn
}

output "lb_id" {
  description = "The ID of the load balancer"
  value       = aws_lb.alb.id
}

output "dns_name" {
  description = "The DNS name of the load balancer"
  value       = aws_lb.alb.dns_name
}

output "zone_id" {
  description = "The canonical hosted zone ID of the load balancer"
  value       = aws_lb.alb.zone_id
}

# Target Group Outputs
output "target_group_arns" {
  description = "Map of target group names to their ARNs"
  value       = { for k, v in aws_lb_target_group.this : k => v.arn }
}

output "target_group_names" {
  description = "Map of target group keys to their names"
  value       = { for k, v in aws_lb_target_group.this : k => v.name }
}

# Legacy output for backward compatibility
output "tg_arn" {
  description = "[DEPRECATED] Use target_group_arns instead. ARN of the legacy target group"
  value       = length(aws_lb_target_group.this) > 0 ? values(aws_lb_target_group.this)[0].arn : null
}

# Certificate Outputs
output "certificate_arn" {
  description = "The ARN of the certificate being used (either provided or created)"
  value       = local.certificate_arn
}

output "created_certificate_arn" {
  description = "The ARN of the ACM certificate created by this module (if any)"
  value       = local.should_create_certificate ? aws_acm_certificate.acm_certificate[0].arn : null
}

# Legacy output (with typo fix)
output "cerificate_arn" {
  description = "[DEPRECATED] Use certificate_arn instead. The ARN of the certificate (legacy output with typo)"
  value       = local.certificate_arn
}

# Listener Outputs
output "listener_arns" {
  description = "Map of listener names to their ARNs"
  value       = { for k, v in aws_lb_listener.this : k => v.arn }
}

# Security Group Output
output "security_group_id" {
  description = "The ID of the security group attached to the load balancer"
  value       = var.security_group_name != "" ? module.load_balancer_sg["main"].security_group_id : null
}