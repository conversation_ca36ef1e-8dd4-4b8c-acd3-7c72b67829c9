# Terraform ALB Module

This Terraform module creates an Application Load Balancer (ALB) on AWS.

## ALB Access Logs Configuration Settings

Take note that destination_kms_key_arn should be set var.destination_kms_key_arn instead of aws:kms. This is intended and required by application load balancer access logs settings. For more information, refer to: https://docs.aws.amazon.com/elasticloadbalancing/latest/application/enable-access-logging.html


source                    = "git::ssh://**********************/dbmy/sre/terraform-modules.git//aws/s3?ref=aws-s3-1.4.3"
bucket_name               = local.access_logs_bucket_name
versioning                = var.versioning
s3_bucket_policy          = data.aws_iam_policy_document.s3_policy.json
bucket_acl                = "private"
enable_bucket_replication = var.enable_bucket_replication
s3_bucket_destination_arn = var.s3_bucket_destination_arn
destination_kms_key_arn   = var.destination_kms_key_arn