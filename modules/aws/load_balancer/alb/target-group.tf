module "alb_tg_listener" {
  count                 = var.tg_name == "" ? 0 : 1
  source                = "../target-group"
  tg_name               = var.tg_name
  tg_port               = var.tg_port
  tg_protocol           = var.tg_protocol
  vpc_id                = data.aws_vpc.selected.id
  tg_target_type        = var.tg_target_type
  health_check_port     = var.health_check_port
  health_check_protocol = var.health_check_protocol
  target_id             = var.target_id == "" ? [] : tolist(split(",", replace(var.target_id, " ", "")))
  availability_zone     = var.az == "" ? "" : var.az
  lb_arn                = aws_lb.alb.arn
  listener_port         = var.listener_port
  listening_protocol    = var.listener_protocol
  certificate_arn       = aws_acm_certificate.acm_certificate.arn
  enable_http_listener  = var.enable_http_listener

  tags = {}

  depends_on = [aws_acm_certificate.acm_certificate]
}
