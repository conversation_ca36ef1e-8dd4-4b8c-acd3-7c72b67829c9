variable "logs_prefix" {
  default = ""
}

variable "logs_enabled" {
  default = true
}

variable "internal" {
  default = true
}

variable "enable_deletion_protection" {
  default = false
}

variable "enable_cross_zone_load_balancing" {
  default = true
}

variable "tags_basic" {
  type = map(string)

  default = {}
}
variable "tags_extra" {
  type = map(string)

  default = {}
}

#---------------------------------------------------
# Security Group
#---------------------------------------------------

variable "security_group_name" {
  description = "Security group for loadbalancer"
  type        = string
}

variable "security_group_rules" {}

#---------------------------------------------------
# Target Group
#---------------------------------------------------
variable "vpc_id" {
  type    = string
  default = ""
}

variable "cert_domain" {
  description = "Domain of the certificate"
  type        = string
}

variable "tg_name" {
  description = "Name of the target group"
  type        = string
}

variable "tg_port" {
  description = "Port for the target group"
  type        = number
  default     = 443
}

variable "tg_protocol" {
  description = "Protocol for the target group"
  type        = string
  default     = "HTTPS"
}

variable "tg_target_type" {
  description = "Target Group Type"
  type        = string
}

variable "health_check_port" {
  description = "Health check port for the target"
  type        = string
  default     = "443"
}

variable "health_check_protocol" {
  description = "Health check protocol for the target"
  type        = string
  default     = "HTTPS"
}

variable "target_id" {
  description = "ID of the target to attach with ALB"
}

variable "az" {
  description = "Availability Zone Details"
}

variable "listener_port" {
  description = "Listener Port"
  type        = string
  default     = "443"
}

variable "listener_protocol" {
  description = "Listener Protocol"
  type        = string
  default     = "HTTPS"
}

variable "enable_http_listener" {
  type    = bool
  default = false
}

#---------------------------------------------------
# S3 Bucket Access Logs
#---------------------------------------------------
variable "versioning" {
  default = false
}

variable "enable_bucket_replication" {
  type    = bool
  default = false
}

variable "destination_kms_key_arn" {
  type    = string
  default = ""
}

variable "s3_bucket_destination_arn" {
  description = "ARN of destination S3 Bucket you're copying the files to"
  type        = string
  default     = ""
}

#---------------------------------------------------
# General Variables
#---------------------------------------------------
variable "environment" {
  description = "The deployment environment"
  type        = string
}

variable "account_name" {
  description = "Name of Account"
}

variable "service_name" {
  description = "Name of Service"
  type        = string
}

variable "route53_zone" {
  description = "DNS Zone name"
  type        = string
}

variable "enable_route53_alb" {
  type    = bool
  default = true
}
