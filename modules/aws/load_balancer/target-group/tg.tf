resource "aws_lb_target_group" "lb-tg01" {
  name                          = var.tg_name
  port                          = var.tg_port
  protocol                      = var.tg_protocol
  vpc_id                        = var.vpc_id
  deregistration_delay          = var.deregistration_delay
  target_type                   = var.tg_target_type
  load_balancing_algorithm_type = var.load_balancing_algorithm_type
  tags                          = var.tags

  health_check {
    port                = var.health_check_port
    protocol            = var.health_check_protocol
    healthy_threshold   = var.healthy_threshold
    interval            = var.hc_interval
    matcher             = var.health_check_protocol == "HTTP" || var.health_check_protocol == "HTTPS" ? var.hc_matcher : null
    path                = var.health_check_protocol == "HTTP" || var.health_check_protocol == "HTTPS" ? var.hc_path : null
    timeout             = var.hc_timeout
    unhealthy_threshold = var.unhealthy_threshold
  }
}

resource "aws_lb_target_group_attachment" "lb_tg_attachment" {
  count             = var.target_id == "" ? 0 : 1
  target_group_arn  = aws_lb_target_group.lb-tg01.arn
  target_id         = element(var.target_id, count.index)
  port              = var.tg_target_type == "lambda" ? null : var.tg_port
  availability_zone = try("${var.availability_zone}", null)
}

resource "aws_lb_listener" "lb_lb_listener01" {
  load_balancer_arn = var.lb_arn
  port              = var.listener_port
  protocol          = var.listening_protocol
  certificate_arn   = try(var.certificate_arn, null)
  ssl_policy        = var.listening_protocol == "HTTPS" || var.listening_protocol == "TLS" ? var.ssl_policy : null
  alpn_policy       = var.listening_protocol == "TLS" ? var.alpn_policy : null
  tags              = var.tags
  default_action {
    target_group_arn = aws_lb_target_group.lb-tg01.arn
    type             = "forward"
  }
}
## Listener for Port 80 (http) - Optional Feature
resource "aws_lb_listener" "lb_listener_port_http" {
  count             = var.enable_http_listener ? 1 : 0
  load_balancer_arn = var.lb_arn
  port              = 80
  protocol          = "HTTP"
  tags              = var.tags
  default_action {
    target_group_arn = aws_lb_target_group.lb-tg01.arn
    type             = "forward"
  }
}
