variable "tg_name" {}
variable "vpc_id" {}
variable "target_id" {
  default = []
}

variable "availability_zone" {
  default = ""
}
variable "tg_protocol" {
  default = "TCP"
}
variable "tg_target_type" {
  default = "instance"
}
variable "certificate_arn" {
  default = ""
}
variable "lb_arn" {}
variable "tg_port" {
  type = number
}
variable "listener_port" {
  type = number
}
variable "health_check_port" {}

variable "health_check_protocol" {
  default = "TCP"
}

variable "listening_protocol" {
  default = "TCP"
}

variable "deregistration_delay" {
  default = "300"
}

variable "load_balancing_algorithm_type" {
  default = "round_robin"
}

variable "healthy_threshold" {
  default = "5"
}

variable "hc_interval" {
  default = "300"
}

variable "hc_timeout" {
  default = "30"
}

variable "unhealthy_threshold" {
  default = "5"
}

variable "hc_matcher" {
  default = "200,202"
}

variable "hc_path" {
  default = "/"
}

variable "ssl_policy" {
  default = "ELBSecurityPolicy-FS-1-2-2019-08"
}

variable "alpn_policy" {
  default = null
}

variable "enable_http_listener" {
  type    = bool
  default = false
}

variable "tags" {
  type    = map(string)
  default = {}
}
