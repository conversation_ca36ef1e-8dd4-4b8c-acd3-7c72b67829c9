locals {
  create_bucket = var.create_bucket

  lifecycle_rules = length(var.lifecycle_rules) > 0 ? var.lifecycle_rules : [
    {
      id      = "placeholer"
      enabled = true
      filter = {
        prefix = "glacier-me/"
      }
      transition = [{
        days          = 30
        storage_class = "GLACIER"
      }]
    }
  ]

  bucket_name = replace(var.bucket_name, ".", "-")

  create_bucket_acl = (var.bucket_acl != null && var.bucket_acl != "null") || length(local.grants) > 0

  # Variables with type `any` should be jsonencode()'d when value is coming from Terragrunt
  grants              = try(jsondecode(var.grant), var.grant)
  intelligent_tiering = try(jsondecode(var.intelligent_tiering), var.intelligent_tiering)
}
