#!/usr/bin/env python3
import argparse
import json
import logging
import sys
from typing import Optional

import boto3
from botocore.exceptions import ClientError
import colorlog

AWS_REGION = "ap-southeast-5"
BUCKET_NAME_TEMPLATE = "tabstudio-terraform-state"
DDB_TABLE_NAME_TEMPLATE = "tabstudio-terraform-locks"
ROLE_NAME_TEMPLATE = "terraform"
ENVS = ["dev", "stg", "prd"]
# Get the sso role arn by aws iam list-roles --query "Roles[?contains(RoleName, 'AWSReservedSSO_AdministratorAccess')].Arn" --output text --profile <profile>
ADMIN_ROLE_ARN_TEMPLATE = "arn:aws:iam::<ACCOUNT_ID>:role/aws-reserved/sso.amazonaws.com/ap-southeast-5/AWSReservedSSO_AdministratorAccess_416f3c4e29e5a28c"

logger = logging.getLogger(__name__)
# add stdout handler
logger_handler = logging.StreamHandler(sys.stdout)
# add colorlog formatter
formatter = colorlog.ColoredFormatter(
    "%(log_color)s%(levelname)s%(reset)s %(asctime)s - %(message)s",
    datefmt="%H:%M:%S",
    log_colors={
        "DEBUG": "cyan",
        "INFO": "green",
        "WARNING": "yellow",
        "ERROR": "red",
        "CRITICAL": "bold_red",
    },
)
logger_handler.setFormatter(formatter)
logger.addHandler(logger_handler)
logger.setLevel(logging.INFO)


def get_session(profile: str, region: str) -> boto3.session.Session:
    if not profile:
        logger.error("AWS profile is required. Pass --profile <your_profile>.")
        sys.exit(2)
    if not region:
        logger.error("AWS region is required. Pass --region <your_region>.")
        sys.exit(2)
    kwargs = {"profile_name": profile}
    if region:
        kwargs["region_name"] = region
    try:
        return boto3.session.Session(**kwargs)
    except Exception as e:
        logger.error(f"Failed to create boto3 session for profile '{profile}': {e}")
        sys.exit(2)


def get_session_details(session: boto3.session.Session) -> dict:
    details = {}
    sts = session.client("sts")
    identity = sts.get_caller_identity()
    details["account_id"] = identity["Account"]
    details["user_arn"] = identity["Arn"]
    details["username"] = identity["Arn"].split("/")[-1]
    if details["username"] is None:
        logger.error("Failed to get username from ARN: %s", identity["Arn"])
        sys.exit(2)
    return details


def create_s3(session: boto3.session.Session, region: str, env: str):
    if env is None or env not in ENVS:
        logger.error(f"Environment is required. Pass --env <{"|".join(ENVS)}>.")
        sys.exit(2)
    s3 = session.client("s3", region_name=region)
    bucket_name = f"{BUCKET_NAME_TEMPLATE}-{env}"
    logger.info(f"Creating S3 bucket '{bucket_name}' in {region} (if not exists)...")
    try:
        if region == "us-east-1":
            s3.create_bucket(Bucket=bucket_name)
        else:
            s3.create_bucket(
                Bucket=bucket_name,
                CreateBucketConfiguration={"LocationConstraint": region},
            )
        logger.info("Bucket created.")
    except ClientError as e:
        code = e.response.get("Error", {}).get("Code")
        if code in ("BucketAlreadyOwnedByYou", "BucketAlreadyExists"):
            logger.info("Bucket already exists; continuing.")
        else:
            logger.error(f"Failed to create bucket: {e}")
            sys.exit(1)
    logger.info("Configuring bucket versioning...")
    # Enable versioning
    try:
        s3.put_bucket_versioning(
            Bucket=bucket_name, VersioningConfiguration={"Status": "Enabled"}
        )
        logger.info("Bucket versioning enabled.")
    except ClientError as e:
        logger.error(f"Failed to enable versioning: {e}")
        sys.exit(1)

    logger.info("Configuring bucket encryption...")
    # Enable default encryption (SSE-S3)
    try:
        s3.put_bucket_encryption(
            Bucket=bucket_name,
            ServerSideEncryptionConfiguration={
                "Rules": [
                    {
                        "ApplyServerSideEncryptionByDefault": {
                            "SSEAlgorithm": "AES256"
                        }
                    }
                ]
            },
        )
        logger.info("Bucket encryption (AES256) enabled.")
    except ClientError as e:
        logger.error(f"Failed to set bucket encryption: {e}")
        sys.exit(1)

    logger.info("S3 bucket setup complete.")


def create_ddb(session: boto3.session.Session, region: str, env: str):
    if env is None or env not in ENVS:
        logger.error(f"Environment is required. Pass --env <{"|".join(ENVS)}>.")
        sys.exit(2)
    ddb = session.client("dynamodb", region_name=region)
    table_name = f"{DDB_TABLE_NAME_TEMPLATE}-{env}"
    logger.info(f"Creating DynamoDB table '{table_name}' (if not exists)...")
    try:
        ddb.create_table(
            TableName=table_name,
            AttributeDefinitions=[{"AttributeName": "LockID", "AttributeType": "S"}],
            KeySchema=[{"AttributeName": "LockID", "KeyType": "HASH"}],
            BillingMode="PAY_PER_REQUEST",
        )
        waiter = ddb.get_waiter("table_exists")
        waiter.wait(TableName=table_name)
        logger.info("DynamoDB table created and is now active.")
    except ClientError as e:
        code = e.response.get("Error", {}).get("Code")
        if code in ("ResourceInUseException",):
            logger.info("DynamoDB table already exists; continuing.")
        else:
            logger.error(f"Failed to create DynamoDB table: {e}")
            sys.exit(1)


def create_role(session: boto3.session.Session, env: str):
    if env is None or env not in ENVS:
        logger.error(f"Environment is required. Pass --env <{"|".join(ENVS)}>.")
        sys.exit(2)
    iam = session.client("iam")
    account_id = get_session_details(session)["account_id"]
    principal_arn = ADMIN_ROLE_ARN_TEMPLATE.replace("<ACCOUNT_ID>", account_id)
    trust_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {"AWS": principal_arn},
                "Action": "sts:AssumeRole",
            }
        ],
    }
    role_name = f"{ROLE_NAME_TEMPLATE}-{env}"
    logger.info(f"Creating IAM role '{role_name}' (if not exists)...")
    try:
        iam.create_role(
            RoleName=role_name,
            AssumeRolePolicyDocument=json.dumps(trust_policy),
            Description="Role for Terraform to access backend resources",
        )
        logger.info("IAM role created.")
    except ClientError as e:
        code = e.response.get("Error", {}).get("Code")
        if code == "EntityAlreadyExists":
            logger.info("IAM role already exists; continuing.")
        else:
            logger.error(f"Failed to create IAM role: {e}")
            sys.exit(1)


def attach_policy(session: boto3.session.Session, region: str, env: str):
    iam = session.client("iam")
    bucket_name = f"{BUCKET_NAME_TEMPLATE}-{env}"
    table_name = f"{DDB_TABLE_NAME_TEMPLATE}-{env}"
    account_id = get_session_details(session)["account_id"]
    policy_doc = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Action": [
                    "s3:GetObject",
                    "s3:PutObject",
                    "s3:DeleteObject",
                    "s3:ListBucket",
                ],
                "Resource": [
                    f"arn:aws:s3:::{bucket_name}",
                    f"arn:aws:s3:::{bucket_name}/*",
                ],
            },
            {
                "Effect": "Allow",
                "Action": [
                    "dynamodb:PutItem",
                    "dynamodb:GetItem",
                    "dynamodb:DeleteItem",
                    "dynamodb:Scan",
                    "dynamodb:Query",
                    "dynamodb:UpdateItem",
                ],
                "Resource": f"arn:aws:dynamodb:{region}:{account_id}:table/{table_name}",
            },
            {
                "Effect": "Allow",
                "Action": "*",
                "Resource": "*",
            },
        ],
    }
    role_name = f"{ROLE_NAME_TEMPLATE}-{env}"
    logger.info("Attaching inline policy to IAM role (put-role-policy)...")
    try:
        iam.put_role_policy(
            RoleName=role_name,
            PolicyName="TerraformPolicy",
            PolicyDocument=json.dumps(policy_doc),
        )
        logger.info("IAM inline policy attached/updated.")
    except ClientError as e:
        logger.error(f"Failed to attach inline policy: {e}")
        sys.exit(1)


def assume_role(session: boto3.session.Session, region: str, env: str):
    sts = session.client("sts")
    account_id = get_session_details(session)['account_id']
    role_name = f"{ROLE_NAME_TEMPLATE}-{env}"
    role_arn = f"arn:aws:iam::{account_id}:role/{role_name}"
    logger.info(f"Assuming role: {role_arn}")
    try:
        resp = sts.assume_role(RoleArn=role_arn, RoleSessionName="terraform-session")
    except ClientError as e:
        logger.error(f"Failed to assume role: {e}")
        sys.exit(1)

    creds = resp["Credentials"]
    logger.info("Export these environment variables:")
    print(f"export AWS_ACCESS_KEY_ID={creds['AccessKeyId']}")
    print(f"export AWS_SECRET_ACCESS_KEY={creds['SecretAccessKey']}")
    print(f"export AWS_SESSION_TOKEN={creds['SessionToken']}")


def main():
    parser = argparse.ArgumentParser(description="Terraform AWS Backend Setup CLI (boto3)")
    parser.add_argument(
        "--profile",
        required=True,
        help="AWS profile name to use (required)",
    )
    parser.add_argument(
        "--region",
        required=True,
        help=f"AWS region to use (default: {AWS_REGION})",
    )
    parser.add_argument(
        "--env",
        required=True,
        help="Environment to use (dev, stg, prd)",
    )
    parser.add_argument(
        "--action",
        required=True,
        choices=[
            "create-s3",
            "create-ddb",
            "create-role",
            "attach-policy",
            "assume-role",
            "inspect-sessions",
            "all",
        ],
        help="Action to perform",
    )

    args = parser.parse_args()
    env = args.env

    session = get_session(args.profile, args.region)

    if args.action == "create-s3":
        create_s3(session, args.region, env=env)
    elif args.action == "create-ddb":
        create_ddb(session, args.region, env=env)
    elif args.action == "create-role":
        create_role(session, env=env)
    elif args.action == "attach-policy":
        attach_policy(session, args.region, env=env)
    elif args.action == "assume-role":
        assume_role(session, args.region, env=env)
    elif args.action == "inspect-sessions":
        session_details = get_session_details(session)
        logger.info(f"Session details: {session_details}")
    elif args.action == "all":
        create_s3(session, args.region, env=env)
        create_ddb(session, args.region, env=env)
        create_role(session, env=env)
        attach_policy(session, args.region, env=env)
        logger.info("All resources created.")


if __name__ == "__main__":
    main()
