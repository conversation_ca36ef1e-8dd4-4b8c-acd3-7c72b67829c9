module "wrapper" {
  source = "../../modules/container-definition"

  for_each = var.items

  cloudwatch_log_group_class             = try(each.value.cloudwatch_log_group_class, var.defaults.cloudwatch_log_group_class, null)
  cloudwatch_log_group_kms_key_id        = try(each.value.cloudwatch_log_group_kms_key_id, var.defaults.cloudwatch_log_group_kms_key_id, null)
  cloudwatch_log_group_name              = try(each.value.cloudwatch_log_group_name, var.defaults.cloudwatch_log_group_name, null)
  cloudwatch_log_group_retention_in_days = try(each.value.cloudwatch_log_group_retention_in_days, var.defaults.cloudwatch_log_group_retention_in_days, 14)
  cloudwatch_log_group_use_name_prefix   = try(each.value.cloudwatch_log_group_use_name_prefix, var.defaults.cloudwatch_log_group_use_name_prefix, false)
  command                                = try(each.value.command, var.defaults.command, null)
  cpu                                    = try(each.value.cpu, var.defaults.cpu, null)
  create_cloudwatch_log_group            = try(each.value.create_cloudwatch_log_group, var.defaults.create_cloudwatch_log_group, true)
  dependsOn                              = try(each.value.dependsOn, var.defaults.dependsOn, null)
  disableNetworking                      = try(each.value.disableNetworking, var.defaults.disableNetworking, null)
  dnsSearchDomains                       = try(each.value.dnsSearchDomains, var.defaults.dnsSearchDomains, null)
  dnsServers                             = try(each.value.dnsServers, var.defaults.dnsServers, null)
  dockerLabels                           = try(each.value.dockerLabels, var.defaults.dockerLabels, null)
  dockerSecurityOptions                  = try(each.value.dockerSecurityOptions, var.defaults.dockerSecurityOptions, null)
  enable_cloudwatch_logging              = try(each.value.enable_cloudwatch_logging, var.defaults.enable_cloudwatch_logging, true)
  enable_execute_command                 = try(each.value.enable_execute_command, var.defaults.enable_execute_command, false)
  entrypoint                             = try(each.value.entrypoint, var.defaults.entrypoint, null)
  environment                            = try(each.value.environment, var.defaults.environment, null)
  environmentFiles                       = try(each.value.environmentFiles, var.defaults.environmentFiles, null)
  essential                              = try(each.value.essential, var.defaults.essential, null)
  extraHosts                             = try(each.value.extraHosts, var.defaults.extraHosts, null)
  firelensConfiguration                  = try(each.value.firelensConfiguration, var.defaults.firelensConfiguration, null)
  healthCheck                            = try(each.value.healthCheck, var.defaults.healthCheck, null)
  hostname                               = try(each.value.hostname, var.defaults.hostname, null)
  image                                  = try(each.value.image, var.defaults.image, null)
  interactive                            = try(each.value.interactive, var.defaults.interactive, false)
  links                                  = try(each.value.links, var.defaults.links, null)
  linuxParameters                        = try(each.value.linuxParameters, var.defaults.linuxParameters, {})
  logConfiguration                       = try(each.value.logConfiguration, var.defaults.logConfiguration, {})
  memory                                 = try(each.value.memory, var.defaults.memory, null)
  memoryReservation                      = try(each.value.memoryReservation, var.defaults.memoryReservation, null)
  mountPoints                            = try(each.value.mountPoints, var.defaults.mountPoints, null)
  name                                   = try(each.value.name, var.defaults.name, null)
  operating_system_family                = try(each.value.operating_system_family, var.defaults.operating_system_family, "LINUX")
  portMappings                           = try(each.value.portMappings, var.defaults.portMappings, null)
  privileged                             = try(each.value.privileged, var.defaults.privileged, false)
  pseudoTerminal                         = try(each.value.pseudoTerminal, var.defaults.pseudoTerminal, false)
  readonlyRootFilesystem                 = try(each.value.readonlyRootFilesystem, var.defaults.readonlyRootFilesystem, true)
  region                                 = try(each.value.region, var.defaults.region, null)
  repositoryCredentials                  = try(each.value.repositoryCredentials, var.defaults.repositoryCredentials, null)
  resourceRequirements                   = try(each.value.resourceRequirements, var.defaults.resourceRequirements, null)
  restartPolicy = try(each.value.restartPolicy, var.defaults.restartPolicy, {
    enabled = true
  })
  secrets            = try(each.value.secrets, var.defaults.secrets, null)
  service            = try(each.value.service, var.defaults.service, null)
  startTimeout       = try(each.value.startTimeout, var.defaults.startTimeout, 30)
  stopTimeout        = try(each.value.stopTimeout, var.defaults.stopTimeout, 120)
  systemControls     = try(each.value.systemControls, var.defaults.systemControls, null)
  tags               = try(each.value.tags, var.defaults.tags, {})
  ulimits            = try(each.value.ulimits, var.defaults.ulimits, null)
  user               = try(each.value.user, var.defaults.user, null)
  versionConsistency = try(each.value.versionConsistency, var.defaults.versionConsistency, "disabled")
  volumesFrom        = try(each.value.volumesFrom, var.defaults.volumesFrom, null)
  workingDirectory   = try(each.value.workingDirectory, var.defaults.workingDirectory, null)
}
